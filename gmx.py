from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.by import By
from anticaptchaofficial.imagecaptcha import imagecaptcha
from data_generator import DataGenerator
from twocaptcha import TwoCaptcha
import os, random, logging, json, psutil, secrets, requests, base64, tempfile, time
from datetime import datetime
from logging.handlers import RotatingFileHandler
from fivesim_integration import FiveSimManager
import shutil, msvcrt, sys, re
from random import randint, uniform
from PIL import Image, ImageGrab
from time import sleep
import pyautogui
import numpy as np
import cv2
import secrets
from ai import solve_captcha_automatically
from api_key_manager import get_api_manager


# Disable PyAutoGUI failsafe to prevent script interruption when mouse moves to top-left corner
pyautogui.FAILSAFE = False

# OpenCV library for visual template matching
CV2_AVAILABLE = True
PIL_AVAILABLE = True
FIVESIM_AVAILABLE = True


# Enhanced SeleniumBase Driver - Primary Interface
try:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from updated_driver import EnhancedSeleniumBaseDriver
    ENHANCED_DRIVER_AVAILABLE = True
    print("Enhanced SeleniumBase Driver and components imported successfully")
except ImportError as e:
    print(f"First Enhanced driver not available: {str(e)}")
    print("Migration cannot proceed without enhanced driver components")
    ENHANCED_DRIVER_AVAILABLE = False

# Proxy Management System
try:
    from proxy_manager import ProxyManager
    PROXY_MANAGER_AVAILABLE = True
    print("Proxy Management System imported successfully")
except ImportError as e:
    print(f"Proxy Manager not available: {str(e)}")
    PROXY_MANAGER_AVAILABLE = False




os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home
gmx_map_file = f"{home}/credentials/gmx_accounts.txt"
proxy_file = f"{home}/proxy.txt"
settings_path = f"{home}/json/settings.json"
cp_xpaths = [
    "//div[@id='rc-anchor-container']",
    "//div[@id='recaptcha-accessible-status']",
    "//span[contains(@class, 'recaptcha-checkbox')]",
    
]


class UnicodeStreamHandler(logging.StreamHandler):
    """Custom StreamHandler that handles Unicode characters properly on Windows"""

    def emit(self, record):
        try:
            msg = self.format(record)
            # Replace Unicode emojis with ASCII equivalents for console output
            msg = msg.replace('✅', '[OK]').replace('❌', '[FAIL]').replace('️', '[WARN]')
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)


def setup_logging():
    """
    Setup centralized logging configuration for the entire application.
    This ensures all modules use the same logging configuration and write to the correct file.
    """
    # Clear any existing handlers to avoid conflicts
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create file handler with rotation (supports Unicode)
    log_file = 'gmx_creator.log'
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'  # Ensure file handler supports Unicode
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # Create console handler with Unicode support
    console_handler = UnicodeStreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.root.setLevel(logging.DEBUG)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    # Prevent duplicate logs from other libraries
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)

    return logging.getLogger(__name__)


class Driver():
    """
    Fully Migrated Driver class - Uses EnhancedSeleniumBaseDriver exclusively
    Provides backward compatibility while leveraging all enhanced features
    """
    def __init__(self, first_name, last_name, birthday,password, index):
        # Setup centralized logging
        setup_logging()
        self.logger = logging.getLogger(f"EnhancedDriver-{index}")

        self.first_name = first_name
        self.last_name = last_name
        self.birthday = birthday
        self.password = password
        self.ac = index



        try:
            self.logger.info("Initializing Enhanced SeleniumBase Driver with full feature set")

            # Create enhanced driver instance
            self._enhanced_driver = EnhancedSeleniumBaseDriver()

            # Expose browser for backward compatibility
            self.browser = self._enhanced_driver.browser


            if hasattr(self._enhanced_driver, 'behavioral_simulator'):
                self.logger.info("Behavioral simulation enabled")

            self.logger.info("Enhanced SeleniumBase Driver initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Enhanced SeleniumBase Driver: {str(e)}")
            raise e

    # Enhanced driver handles all proxy configuration internally

    def __del__(self):
        """Cleanup when Driver object is destroyed"""
        try:
            if hasattr(self, '_enhanced_driver') and self._enhanced_driver:
                # Enhanced driver handles its own cleanup
                self._enhanced_driver.quit()
                self.logger.info("Enhanced driver cleanup completed")
        except Exception as e:
            # Use print instead of logger since logger might be destroyed
            print(f"Error during Driver cleanup: {str(e)}")

    def __getattr__(self, name):
        """
        Delegate method calls to enhanced driver for backward compatibility
        This ensures all enhanced driver methods are accessible through the Driver instance
        """
        if hasattr(self, '_enhanced_driver') and hasattr(self._enhanced_driver, name):
            return getattr(self._enhanced_driver, name)
        else:
            # Method not found in enhanced driver
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class Worker():
    def __init__(self,actions):
        super().__init__()
        self.actions = actions
        self.browser = None  # Will be set when Driver is created
        self._enhanced_driver = None  # Will be set when Driver is created
        self.data_generator = DataGenerator()
        # Use centralized logging setup
        self.logger = logging.getLogger("Worker")

        # Initialize proxy manager if available
        self.proxy_manager = None
        if PROXY_MANAGER_AVAILABLE:
            try:
                self.proxy_manager = ProxyManager()
                self.logger.info("Proxy Manager initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Proxy Manager: {str(e)}")
                self.proxy_manager = None

    def _setup_driver_references(self, driver_instance, screen=None):
        """Set up references to the driver and enhanced driver for Worker methods"""
        self.browser = driver_instance
        if hasattr(driver_instance, '_enhanced_driver'):
            self._enhanced_driver = driver_instance._enhanced_driver
        else:
            self._enhanced_driver = None
            self.logger.warning("Enhanced driver not available in Driver instance")


    def _find_element_silent(self, selector, selector_type='xpath', timeout=5):
        """Helper method to find element without throwing exceptions"""
        try:
            if selector_type == 'xpath':
                return WebDriverWait(self.browser, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
            elif selector_type == 'css':
                return WebDriverWait(self.browser, timeout).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
            else:
                return None
        except:
            return None
        

    def _find_elements_silent(self, selector, selector_type='xpath', timeout=5):
        """Helper method to find multiple elements without throwing exceptions"""
        try:
            if selector_type == 'xpath':
                WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                return self.browser.find_elements(By.XPATH, selector)
            elif selector_type == 'css':
                WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                return self.browser.find_elements(By.CSS_SELECTOR, selector)
            else:
                return []
        except:
            return []



    def _get_element_text_content(self, element):
        """
        Get text content from element, handling Chrome custom elements and shadow DOM

        Args:
            element: WebElement to extract text from

        Returns:
            str: Text content of the element
        """
        try:
            # Try multiple methods to get text content
            text_methods = [
                lambda e: e.text,
                lambda e: e.get_attribute('textContent'),
                lambda e: e.get_attribute('innerText'),
                lambda e: e.get_attribute('value'),
                lambda e: e.get_attribute('aria-label'),
                lambda e: e.get_attribute('title')
            ]

            for method in text_methods:
                try:
                    text = method(element)
                    if text and text.strip():
                        return text.strip()
                except:
                    continue

            return ""

        except Exception as e:
            self.logger.debug(f"Error getting element text: {str(e)}")
            return ""




    def _get_browser_window_coordinates(self):
        """Get browser window coordinates for coordinate-based clicking"""
        try:
            # Try to get window position using JavaScript
            try:
                if hasattr(self.browser, 'execute_js'):
                    window_info = self.browser.execute_js("""
                        return {
                            x: window.screenX || window.screenLeft || 0,
                            y: window.screenY || window.screenTop || 0,
                            width: window.outerWidth || 1200,
                            height: window.outerHeight || 800
                        };
                    """)
                elif hasattr(self.browser, 'execute_script'):
                    window_info = self.browser.execute_script("""
                        return {
                            x: window.screenX || window.screenLeft || 0,
                            y: window.screenY || window.screenTop || 0,
                            width: window.outerWidth || 1200,
                            height: window.outerHeight || 800
                        };
                    """)
                else:
                    window_info = None

                if window_info:
                    self.logger.debug(f"Browser window coordinates: {window_info}")
                    return window_info

            except Exception as js_error:
                self.logger.debug(f"JavaScript window detection failed: {str(js_error)}")

            # Fallback: assume standard browser window position
            return {
                'x': 100,
                'y': 100,
                'width': 1200,
                'height': 800
            }

        except Exception as e:
            self.logger.debug(f"Window coordinate detection failed: {str(e)}")
            return None



    def _analyze_button_region(self, region_screenshot):
        """Analyze a screenshot region to detect button-like elements"""
        try:
            # Convert PIL image to numpy array for analysis
            img_array = np.array(region_screenshot)

            # Simple button detection based on color patterns
            # Buttons typically have distinct colors and edges

            # Check for common button colors (blue, white, gray backgrounds)
            # This is a simplified approach - in production you might use more sophisticated image analysis

            # Calculate color variance - buttons typically have consistent colors
            color_variance = np.var(img_array)

            # Check for edge-like patterns that indicate button borders
            # Convert to grayscale for edge detection
            gray = np.mean(img_array, axis=2)
            edges = np.abs(np.diff(gray, axis=0)).sum() + np.abs(np.diff(gray, axis=1)).sum()

            # Simple heuristic: buttons have moderate color variance and clear edges
            is_button_like = (color_variance > 100 and color_variance < 5000) and edges > 50

            self.logger.debug(f"Button analysis - Variance: {color_variance:.1f}, Edges: {edges:.1f}, Button-like: {is_button_like}")

            return is_button_like

        except Exception as e:
            self.logger.debug(f"Button region analysis failed: {str(e)}")
            return False


    def _find_and_click_nested_buttons(self, element):
        """Find and click buttons nested within a Chrome custom element"""
        try:
            # Look for nested buttons or clickable elements
            nested_buttons = element.find_all(['button', 'cr-button', '*[role="button"]'])

            for button in nested_buttons:
                button_text = button.get_text(strip=True)

                if self._is_sync_button_text(button_text):
                    self.logger.info(f"Found nested sync button: '{button_text}'")

                    # Try to click this button
                    if self._click_button_by_attributes(button):
                        return True

            return False

        except Exception as e:
            self.logger.debug(f"Nested button search failed: {str(e)}")
            return False



    def _click_button_by_attributes(self, button_element):
        """Try to click a button using its attributes to build XPath/CSS selectors"""
        try:
            # Build selectors based on button attributes
            selectors = []

            # Try ID-based selector
            button_id = button_element.get('id')
            if button_id:
                selectors.append(f'//*[@id="{button_id}"]')
                selectors.append(f'#{button_id}')

            # Try class-based selector
            button_classes = button_element.get('class', [])
            if button_classes:
                class_str = ' '.join(button_classes)
                selectors.append(f'//*[@class="{class_str}"]')
                selectors.append(f'.{".".join(button_classes)}')

            # Try text-based selector
            button_text = button_element.get_text(strip=True)
            if button_text:
                tag_name = button_element.name
                selectors.append(f'//{tag_name}[contains(text(), "{button_text}")]')
                selectors.append(f'//{tag_name}[normalize-space(.)="{button_text}"]')

            # Try each selector
            for i, selector in enumerate(selectors, 1):
                try:
                    self.logger.debug(f"Trying selector {i}: {selector}")

                    if selector.startswith('//') or selector.startswith('/'):
                        # XPath selector
                        element = self._find_element(selector, 'xpath', timeout=2)
                    else:
                        # CSS selector
                        element = self._find_element(selector, 'css', timeout=2)

                    if element and element.is_displayed():
                        self.logger.info(f"Found element using selector {i}: {selector}")

                        # Click the element
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        sleep(uniform(1.5, 3.0))
                        self.logger.info("HTML parsing: Button clicked successfully")
                        return True

                except Exception as e:
                    self.logger.debug(f"Selector {i} failed: {str(e)}")
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Button clicking by attributes failed: {str(e)}")
            return False




    def _has_silent_detection(self):
        """Check if enhanced driver with silent detection is available"""
        return (self._enhanced_driver is not None and
                hasattr(self._enhanced_driver, 'find_xpath_silent') and
                hasattr(self._enhanced_driver, 'find_css_silent'))



    def terminate_selenium_driver(self):
        """Terminate chromedriver and chrome processes as backup cleanup"""
        try:
            processes_terminated = 0

            # Terminate chromedriver processes
            for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                try:
                    process_info = process.info
                    # Add null check to prevent NoneType error
                    if process_info.get('name') and 'chromedriver' in process_info.get('name').lower():
                        self.logger.info(f"Terminating chromedriver process: {process_info['pid']}")
                        process.terminate()
                        process.wait(timeout=5)
                        processes_terminated += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

            # Also terminate chrome processes that might be orphaned
            for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                try:
                    process_info = process.info
                    if process_info.get('name') and 'chrome' in process_info.get('name').lower():
                        # Check if it's our Chrome process by looking at command line
                        cmdline = process_info.get('cmdline', [])
                        if cmdline and any('--user-data-dir' in str(arg) for arg in cmdline):
                            self.logger.info(f"Terminating Chrome process: {process_info['pid']}")
                            process.terminate()
                            process.wait(timeout=5)
                            processes_terminated += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

            if processes_terminated > 0:
                self.logger.info(f"Terminated {processes_terminated} browser-related processes")
            else:
                self.logger.info("No browser processes found to terminate")

        except Exception as e:
            self.logger.error(f"Error cleaning up browser processes: {str(e)}")



    def wait(self):
        while self.browser.running() == True:
            sleep(2.5)





    def check_js(self, elem):
        """Optimized JavaScript text content checker"""
        var_js = f"return document.body.textContent.includes({elem})"
        try:
            found = self.browser.execute_js(var_js)
            # Handle both boolean and string returns
            return found is True or found == "True" or found == True
        except Exception as e:
            self.logger.debug(f"JavaScript check failed for {elem}: {str(e)}")
            return False




    





    def _validate_captcha_element(self, element, xpath):
        """Validate that a CAPTCHA element is actually displayed and functional"""
        try:
            # Check 1: Element must be displayed/visible
            if not element.is_displayed():
                print(f"CAPTCHA element not displayed: {xpath}")
                #return False

            # Check 2: For image elements, verify src attribute exists and is not empty
            if xpath.endswith('//img') or '//img[' in xpath or xpath.endswith("'captchaimg']"):
                src = element.get_attribute('src')
                if not src or src.strip() == '':
                    self.logger.debug(f"CAPTCHA image has no src attribute: {xpath}")
                    return False

                # Check for placeholder or empty images
                if src in ['', 'about:blank', 'data:,']:
                    self.logger.debug(f"CAPTCHA image has placeholder src: {src}")
                    return False

                self.logger.debug(f"CAPTCHA image has valid src: {src[:50]}...")


            self.logger.debug(f"CAPTCHA element validation passed: {xpath}")
            return True

        except Exception as e:
            self.logger.debug(f"Error validating CAPTCHA element {xpath}: {str(e)}")
            return False



    def extract_image_captcha(self):
        """Extract image CAPTCHA from the page and save it for processing with enhanced validation"""
        try:
            # Try to find the CAPTCHA image element
            captcha_img = None
            found_xpath = None

            # Primary method: look for img#captchaimg
            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    captcha_img = self._enhanced_driver.find_xpath_silent("//img[@id='captchaimg']")
                else:
                    captcha_img = self.browser.find_xpath("//img[@id='captchaimg']")
                if captcha_img:
                    found_xpath = "//img[@id='captchaimg']"
            except:
                pass

            # Fallback methods if primary fails
            if not captcha_img:
                fallback_xpaths = [
                    "//div[contains(@jscontroller, 'CMcBD')]//img",
                    "//div[contains(@class, 'captcha')]//img",
                    "//img[contains(@src, 'captcha')]"
                ]

                for xpath in fallback_xpaths:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            captcha_img = self._enhanced_driver.find_xpath_silent(xpath)
                        else:
                            captcha_img = self.browser.find_xpath(xpath)
                        if captcha_img:
                            found_xpath = xpath
                            break
                    except:
                        continue

            if not captcha_img:
                self.logger.error("Could not find CAPTCHA image element")
                return None

            # Enhanced validation: verify the element is actually functional
            if not self._validate_captcha_element(captcha_img, found_xpath):
                self.logger.error("CAPTCHA image element found but not functional - failing fast")
                return None

            # Get the image source
            img_src = captcha_img.get_attribute('src')
            if not img_src or img_src.strip() == '':
                self.logger.error("CAPTCHA image has no src attribute - failing fast")
                return None

            # Additional validation for src content
            if img_src in ['', 'about:blank', 'data:,']:
                self.logger.error(f"CAPTCHA image has placeholder src: {img_src} - failing fast")
                return None

            self.logger.info(f"Found CAPTCHA image with src: {img_src[:100]}...")

            # Handle data URLs (base64 encoded images)
            if img_src.startswith('data:image'):
                # Extract base64 data
                try:
                    header, data = img_src.split(',', 1)
                    image_data = base64.b64decode(data)

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(image_data)
                    temp_file.close()

                    self.logger.info(f"CAPTCHA image saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error processing base64 image: {str(e)}")
                    return None

            # Handle regular URLs
            else:
                try:
                    # Download the image
                    response = requests.get(img_src, timeout=10)
                    response.raise_for_status()

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(response.content)
                    temp_file.close()

                    self.logger.info(f"CAPTCHA image downloaded and saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error downloading CAPTCHA image: {str(e)}")
                    return None

        except Exception as e:
            self.logger.error(f"Error extracting image CAPTCHA: {str(e)}")
            return None




    def solve_image_captcha(self, image_path):
        """Solve image CAPTCHA using anti-captcha service with updated API"""

        try:
            # Get configuration
            config = self.get_anticaptcha_config()
            api_key = config['api_key']

            # Initialize anti-captcha solver
            solver = imagecaptcha()
            solver.set_verbose(1)
            solver.set_key(api_key)

            # Check account balance
            try:
                balance = solver.get_balance()
                self.logger.info(f"Anti-captcha balance: ${balance}")

                if balance < 0.01:  # Minimum balance check
                    self.logger.error("Insufficient anti-captcha balance")
                    return None
            except Exception as e:
                self.logger.warning(f"Could not check anti-captcha balance: {str(e)}")

            self.logger.info("Submitting image CAPTCHA to anti-captcha service...")

            # Solve the captcha
            captcha_text = solver.solve_and_return_solution(image_path)

            if captcha_text and captcha_text != 0:
                # Validate the solution before returning
                is_valid, validation_msg = self.validate_captcha_solution(captcha_text)
                if is_valid:
                    self.logger.info(f"Image CAPTCHA solved: {captcha_text}")
                    return captcha_text
                else:
                    self.logger.error(f"Invalid CAPTCHA solution: {validation_msg}")
                    return None
            else:
                error_code = getattr(solver, 'error_code', 'Unknown error')
                self.logger.error(f"Anti-captcha failed: {error_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error solving image CAPTCHA with anti-captcha: {str(e)}")
            return None
        finally:
            # Clean up temporary image file
            try:
                if image_path and os.path.exists(image_path):
                    os.unlink(image_path)
                    self.logger.debug(f"Cleaned up temporary image file: {image_path}")
            except Exception as e:
                self.logger.warning(f"Could not clean up temporary file {image_path}: {str(e)}")



    def submit_image_captcha(self, captcha_solution):
        """Submit the solved image CAPTCHA to the form"""
        try:
            # Find the CAPTCHA input field
            captcha_input = None

            # Primary method: look for input[name="ca"]
            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    captcha_input = self._enhanced_driver.find_xpath_silent("//input[@name='ca']")
                else:
                    captcha_input = self.browser.find_xpath("//input[@name='ca']")
            except:
                pass

            # Fallback methods
            if not captcha_input:
                fallback_selectors = [
                    "//input[@id='ca']",
                    "//input[contains(@placeholder, 'captcha')]",
                    "//input[contains(@placeholder, 'text')]"
                ]

                for selector in fallback_selectors:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            captcha_input = self._enhanced_driver.find_xpath_silent(selector)
                        else:
                            captcha_input = self.browser.find_xpath(selector)
                        if captcha_input:
                            break
                    except:
                        continue

            if not captcha_input:
                self.logger.error("Could not find CAPTCHA input field")
                return False

            # Clear any existing text and enter the solution
            try:
                captcha_input.clear()
                sleep(0.5)

                # Use human-like typing if available
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(captcha_input, captcha_solution)
                else:
                    captcha_input.send_keys(captcha_solution)

                self.logger.info(f"Entered CAPTCHA solution: {captcha_solution}")
                sleep(1)

            except Exception as e:
                self.logger.error(f"Error entering CAPTCHA solution: {str(e)}")
                return False

            # Try to find and handle the hidden token field (ct)
            try:
                token_field = None
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    token_field = self._enhanced_driver.find_xpath_silent("//input[@name='ct']")
                else:
                    try:
                        token_field = self.browser.find_xpath("//input[@name='ct']")
                    except:
                        pass

                if token_field:
                    token_value = token_field.get_attribute('value')
                    self.logger.info(f"Found hidden token field with value: {token_value[:20]}...")
                else:
                    self.logger.info("No hidden token field found (this may be normal)")

            except Exception as e:
                self.logger.debug(f"Error checking token field: {str(e)}")

            # Submit the form
            try:
                # Try to find and click the submit button
                submit_button = None
                submit_selectors = [
                    "//button[contains(text(), 'Next')]",
                    "//button[contains(text(), 'Suivant')]",
                    "//input[@type='submit']",
                    "//button[@type='submit']",
                    "//div[@role='button'][contains(text(), 'Next')]",
                    "//div[@role='button'][contains(text(), 'Suivant')]"
                ]

                for selector in submit_selectors:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            submit_button = self._enhanced_driver.find_xpath_silent(selector)
                        else:
                            try:
                                submit_button = self.browser.find_xpath(selector)
                            except:
                                pass
                        if submit_button:
                            break
                    except:
                        continue

                if submit_button:
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(submit_button)
                    else:
                        submit_button.click()
                    self.logger.info("Clicked submit button after entering CAPTCHA")
                else:
                    # Try submitting with Enter key
                    captcha_input.send_keys(Keys.ENTER)
                    self.logger.info("Submitted CAPTCHA with Enter key")

                sleep(2)
                return True

            except Exception as e:
                self.logger.error(f"Error submitting CAPTCHA form: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error in submit_image_captcha: {str(e)}")
            return False



    def ImageCaptchaSolver(self, max_retries=3, retry_delay=5):
        """Complete image CAPTCHA solving workflow with enhanced validation and retry mechanism"""

        # Pre-validation: Double-check that CAPTCHA is actually required
        if not self.ImageCaptchaVerif():
            self.logger.info("Image CAPTCHA verification failed on re-check - no active CAPTCHA found")
            return True  # Not an error, just no CAPTCHA to solve

        for attempt in range(max_retries):
            try:
                self.logger.warning(f"### Starting Image CAPTCHA solving process (Attempt {attempt + 1}/{max_retries}) ###")

                # Step 1: Extract the CAPTCHA image with enhanced validation
                image_path = self.extract_image_captcha()
                if not image_path:
                    # Check if this is a validation failure (element exists but not functional)
                    # vs a genuine missing element
                    if self._is_captcha_element_present_but_invalid():
                        self.logger.warning("CAPTCHA element present but not functional - skipping retries")
                        return True  # Don't retry for false positives

                    self.logger.error(f"Failed to extract CAPTCHA image (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

                # Step 2: Solve the CAPTCHA using anti-captcha service
                captcha_solution = self.solve_image_captcha(image_path)
                if not captcha_solution:
                    self.logger.error(f"Failed to solve image CAPTCHA (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

                # Step 3: Submit the solution
                success = self.submit_image_captcha(captcha_solution)
                if success:
                    self.logger.info("### Image CAPTCHA solved and submitted successfully! ###")
                    return True
                else:
                    self.logger.error(f"Failed to submit image CAPTCHA solution (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

            except Exception as e:
                self.logger.error(f"Error in ImageCaptchaSolver (Attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    self.logger.info(f"Retrying in {retry_delay} seconds...")
                    sleep(retry_delay)
                    continue
                return False

        self.logger.error(f"Image CAPTCHA solving failed after {max_retries} attempts")
        return False




    



    def get_anticaptcha_config(self):
        """Get anti-captcha configuration from settings or use defaults"""
        try:
            # Try to load from enhanced settings first
            if os.path.exists(f"{home}/json/enhanced_settings.json"):
                with open(f"{home}/json/enhanced_settings.json", 'r') as f:
                    settings = json.load(f)
                    captcha_config = settings.get('captcha', {})

                    return {
                        'api_key': captcha_config.get('api_key'),
                        'max_solve_time': captcha_config.get('max_solve_time', 120),
                        'auto_retry': captcha_config.get('auto_retry', True),
                        'service': captcha_config.get('service', 'anticaptcha')
                    }
        except Exception as e:
            self.logger.debug(f"Could not load captcha config from settings: {str(e)}")

        # Return default configuration
        return {
            'api_key': "d315b270071ccc3922a75b7c56e72da1",
            'max_solve_time': 120,
            'auto_retry': True,
            'service': 'anticaptcha'
        }
    


    def validate_captcha_solution(self, solution):
        """Validate CAPTCHA solution before submission"""
        if not solution:
            return False, "Empty solution"

        # Basic validation rules
        if len(solution) < 3:
            return False, "Solution too short"

        if len(solution) > 10:
            return False, "Solution too long"

        # Check for suspicious patterns
        if solution.lower() in ['error', 'failed', 'timeout']:
            return False, "Invalid solution pattern"

        return True, "Valid solution"

    def log_successful_user_agent(self, email=None):
        """
        Log the user-agent used for successful account creation.

        Args:
            email (str, optional): The email address that was successfully created
        """
        try:
            # Get the current user-agent from the browser
            current_user_agent = None

            # Try to get user-agent from enhanced driver first
            if hasattr(self, '_enhanced_driver') and self._enhanced_driver:
                try:
                    # Try to execute JavaScript to get the user agent
                    if hasattr(self._enhanced_driver.browser, 'execute_script'):
                        current_user_agent = self._enhanced_driver.browser.execute_script("return navigator.userAgent;")
                    elif hasattr(self._enhanced_driver.browser, 'execute_js'):
                        current_user_agent = self._enhanced_driver.browser.execute_js("return navigator.userAgent;")
                except Exception as e:
                    self.logger.debug(f"Could not get user-agent from enhanced driver: {str(e)}")

            # Fallback to regular browser if enhanced driver fails
            if not current_user_agent and hasattr(self, 'browser') and self.browser:
                try:
                    if hasattr(self.browser, 'execute_script'):
                        current_user_agent = self.browser.execute_script("return navigator.userAgent;")
                    elif hasattr(self.browser, 'execute_js'):
                        current_user_agent = self.browser.execute_js("return navigator.userAgent;")
                except Exception as e:
                    self.logger.debug(f"Could not get user-agent from browser: {str(e)}")

            if not current_user_agent:
                self.logger.warning("Could not retrieve current user-agent for logging")
                return

            # Prepare log entry
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            log_entry = {
                "timestamp": timestamp,
                "user_agent": current_user_agent,
                "email": email if email else "unknown",
                "success": True
            }

            # Define log file path
            success_ua_log = f"{home}/successful_user_agents.json"

            # Load existing log entries or create new list
            log_entries = []
            if os.path.exists(success_ua_log):
                try:
                    with open(success_ua_log, 'r', encoding='utf-8') as f:
                        log_entries = json.load(f)
                except Exception as e:
                    self.logger.warning(f"Could not load existing user-agent log: {str(e)}")
                    log_entries = []

            # Add new entry
            log_entries.append(log_entry)

            # Keep only the last 1000 entries to prevent file from growing too large
            if len(log_entries) > 1000:
                log_entries = log_entries[-1000:]

            # Save updated log
            try:
                with open(success_ua_log, 'w', encoding='utf-8') as f:
                    json.dump(log_entries, f, indent=2, ensure_ascii=False)

                self.logger.info(f"✅ Successfully logged user-agent for successful account creation")
                self.logger.info(f" User-agent: {current_user_agent[:100]}...")
                if email:
                    self.logger.info(f" Email: {email}")

            except Exception as e:
                self.logger.error(f"Failed to save user-agent log: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error logging successful user-agent: {str(e)}")

    def view_successful_user_agents(self, limit=10):
        """
        View the most recent successful user-agents logged.

        Args:
            limit (int): Number of recent entries to display (default: 10)
        """
        try:
            success_ua_log = f"{home}/successful_user_agents.json"

            if not os.path.exists(success_ua_log):
                self.logger.info("No successful user-agent log file found yet.")
                return

            with open(success_ua_log, 'r', encoding='utf-8') as f:
                log_entries = json.load(f)

            if not log_entries:
                self.logger.info("No successful user-agents logged yet.")
                return

            # Get the most recent entries
            recent_entries = log_entries[-limit:] if len(log_entries) > limit else log_entries

            self.logger.info(f" Showing {len(recent_entries)} most recent successful user-agents:")
            self.logger.info("=" * 80)

            for i, entry in enumerate(reversed(recent_entries), 1):
                timestamp = entry.get('timestamp', 'Unknown')
                user_agent = entry.get('user_agent', 'Unknown')
                email = entry.get('email', 'Unknown')

                self.logger.info(f"{i}. Timestamp: {timestamp}")
                self.logger.info(f"   Email: {email}")
                self.logger.info(f"   User-Agent: {user_agent}")
                self.logger.info("-" * 80)

            # Show statistics
            total_count = len(log_entries)
            unique_uas = len(set(entry.get('user_agent', '') for entry in log_entries))

            self.logger.info(f" Statistics:")
            self.logger.info(f"   Total successful accounts: {total_count}")
            self.logger.info(f"   Unique user-agents used: {unique_uas}")
            self.logger.info(f"   Success rate per UA: {total_count/unique_uas:.2f} accounts per user-agent" if unique_uas > 0 else "   No user-agents recorded")

        except Exception as e:
            self.logger.error(f"Error viewing successful user-agents: {str(e)}")

    def get_user_agent_statistics(self):
        """
        Get detailed statistics about successful user-agents.

        Returns:
            dict: Statistics about user-agent usage
        """
        try:
            success_ua_log = f"{home}/successful_user_agents.json"

            if not os.path.exists(success_ua_log):
                return {"error": "No log file found"}

            with open(success_ua_log, 'r', encoding='utf-8') as f:
                log_entries = json.load(f)

            if not log_entries:
                return {"error": "No entries found"}

            # Count user-agent usage
            ua_counts = {}
            for entry in log_entries:
                ua = entry.get('user_agent', 'Unknown')
                ua_counts[ua] = ua_counts.get(ua, 0) + 1

            # Sort by usage count
            sorted_uas = sorted(ua_counts.items(), key=lambda x: x[1], reverse=True)

            stats = {
                "total_successful_accounts": len(log_entries),
                "unique_user_agents": len(ua_counts),
                "most_successful_ua": sorted_uas[0] if sorted_uas else None,
                "user_agent_distribution": sorted_uas[:10],  # Top 10
                "average_success_per_ua": len(log_entries) / len(ua_counts) if ua_counts else 0
            }

            return stats

        except Exception as e:
            return {"error": f"Error getting statistics: {str(e)}"}


    def solve_captcha(self,url):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        if "signin" in url:
            custom_site_key = "6LdD2OMZAAAAAAv2xVpeCk8yMtBtY3EhDWldrBbh"
        else:
            custom_site_key = "6LctgAgUAAAAACsC7CsLr_jgOWQ2ul2vC_ndi8o2"
        try:
            result = solver.recaptcha(
                sitekey=custom_site_key,
                #url='https://groups.google.com/my-groups?hl=fr-FR'
                url=url
                )
            print(f"This is the result of captcha submission : {result}")

        except Exception as e:
            self.logger.error(f"{str(e)}")

        else:
            #print('solved: ' + str(result))
            #print("Captcha Solved!!")
            return result['code']




    """
    
    def _solve_gmx_captcha(self):
        captcha_solver = CaptchaSolver()
        proxy_config = {
        "type": "http",           # or "https", "socks4", "socks5"
        "address": "*************",
        "port": "12323",
        "username": "14a638c2105fb",
        "password": "10ef8e713c"
        }
        solution = captcha_solver.solve_page_captcha(driver=self.browser, proxy_config=proxy_config)
        return solution is not None
    """




    def _capture_screen_screenshot(self):
        """Capture screenshot using available methods"""
        try:
            # Method 1: PyAutoGUI (primary)
            if 'pyautogui' in globals():
                screenshot = pyautogui.screenshot()
                screenshot_array = np.array(screenshot)
                self.logger.debug(f"Screenshot captured via PyAutoGUI: {screenshot_array.shape}")
                return screenshot_array

            # Method 2: PIL ImageGrab (fallback)
            elif PIL_AVAILABLE:
                screenshot = ImageGrab.grab()
                screenshot_array = np.array(screenshot)
                self.logger.debug(f"Screenshot captured via PIL: {screenshot_array.shape}")
                return screenshot_array

            else:
                self.logger.error("No screenshot capture method available")
                return None

        except Exception as e:
            self.logger.error(f"Screenshot capture failed: {str(e)}")
            return None




    def _get_phone_number(self, country):
        """
        Handle phone number retrieval with unified country-specific logic:
        1. Try to read existing phone number from country-specific file
        2. If found, randomize middle 4 digits (keep first 3 and last 3)
        3. If not found, fallback to 5sim API (except for USA/Canada)
        4. Return clean phone number
        """
        # Country code mapping for file names
        country_codes = {
            'canada': 'ca',
            'germany': 'de',
            'england': 'gb',
            'usa': 'usa'
        }

        country_code = country_codes.get(country, country)
        self.logger.info(f"Getting phone number for {country} (code: {country_code})")

        try:
            # Step 1: Try to get phone number from country-specific file
            #phone_number = self._get_phone_from_file(country_code)
            #if phone_number:
            #    return phone_number

            # Step 2: Fallback logic based on country
            #if country in ['usa', 'canada']:
            #    self.logger.error(f"No local phone numbers available for {country} and 5sim doesn't support this country")
            #    return None
            #else:
                # Step 3: Fallback to 5sim API for other countries
            self.logger.info("Getting new phone number from 5sim API")
            phone_number = self._get_phone_number_with_5sim(country)

            if phone_number:
                # Clean phone number (remove country prefixes)
                clean_phone = phone_number.replace("+44", "").replace("+", "").replace("+49", "")
                self.logger.info(f"Got new phone number from 5sim: {clean_phone}")

                # Apply randomized phone number modification
                modified_phone = self._apply_phone_modification(clean_phone)
                return modified_phone
            else:
                self.logger.error("Failed to get phone number from 5sim")
                return None

        except Exception as e:
            self.logger.error(f"Error in phone number handling: {str(e)}")
            return None



    def _get_phone_from_file(self, country_code):
        """
        Get and randomize phone number from country-specific file.

        Args:
            country_code (str): Country code (ca, de, gb, usa)

        Returns:
            str: Randomized phone number or None if not found
        """
        try:
            phone_file_path = f"credentials/{country_code}_used_phone_numbers.txt"

            if not os.path.exists(phone_file_path):
                self.logger.info(f"Phone numbers file does not exist: {phone_file_path}")
                # Create credentials directory if it doesn't exist
                os.makedirs("credentials", exist_ok=True)
                return None

            with open(phone_file_path, 'r', encoding='utf-8') as f:
                phone_numbers = [line.strip() for line in f.readlines() if line.strip()]

            if not phone_numbers:
                self.logger.info(f"No phone numbers found in file: {phone_file_path}")
                return None

            # Get a random phone number from the file
            original_phone = random.choice(phone_numbers)
            self.logger.info(f"Found existing phone number: {original_phone}")

            # Apply country-specific randomization
            return self._randomize_phone_number(original_phone, country_code)

        except Exception as e:
            self.logger.error(f"Error reading phone numbers file {country_code}: {str(e)}")
            return None



    def _randomize_phone_number(self, original_phone, country_code=None):
        """
        Apply country-specific phone number randomization patterns.

        Args:
            original_phone (str): Original phone number
            country_code (str): Country code (ca, de, gb, usa) for specific patterns

        Returns:
            str: Phone number with randomized digits based on country format
        """
        try:
            phone_length = len(original_phone)

            # Country-specific randomization patterns
            if country_code == 'usa' or country_code == 'ca':
                # USA/Canada: 10 digits (e.g., 3439460609)
                # Pattern: Keep first 3 (area code), randomize middle 3, keep last 4
                if phone_length == 10:
                    area_code = original_phone[:3]  # Keep area code
                    last_four = original_phone[-4:]  # Keep last 4 digits
                    middle_three = ''.join([str(random.randint(0, 9)) for _ in range(3)])
                    modified_phone = f"{area_code}{middle_three}{last_four}"
                    self.logger.info(f"Randomized {country_code.upper()} phone: {original_phone} -> {modified_phone}")
                    return modified_phone

            elif country_code == 'de':
                # Germany: 11 digits (e.g., 15213148486)
                # Pattern: Keep first 3 (mobile prefix), randomize middle 5, keep last 3
                if phone_length == 11:
                    mobile_prefix = original_phone[:3]  # Keep mobile prefix (152, 157, etc.)
                    last_three = original_phone[-3:]  # Keep last 3 digits
                    middle_five = ''.join([str(random.randint(0, 9)) for _ in range(5)])
                    modified_phone = f"{mobile_prefix}{middle_five}{last_three}"
                    self.logger.info(f"Randomized German phone: {original_phone} -> {modified_phone}")
                    return modified_phone
                elif phone_length == 12:
                    # Some German numbers might be 12 digits
                    mobile_prefix = original_phone[:3]  # Keep mobile prefix
                    last_three = original_phone[-3:]  # Keep last 3 digits
                    middle_six = ''.join([str(random.randint(0, 9)) for _ in range(6)])
                    modified_phone = f"{mobile_prefix}{middle_six}{last_three}"
                    self.logger.info(f"Randomized German phone (12-digit): {original_phone} -> {modified_phone}")
                    return modified_phone

            elif country_code == 'gb':
                # UK/England: 10 digits (e.g., 7863600533)
                # Pattern: Keep first 4 (mobile prefix 786x), randomize middle 3, keep last 3
                if phone_length == 10:
                    mobile_prefix = original_phone[:4]  # Keep mobile prefix (786x, 787x, etc.)
                    last_three = original_phone[-3:]  # Keep last 3 digits
                    middle_three = ''.join([str(random.randint(0, 9)) for _ in range(3)])
                    modified_phone = f"{mobile_prefix}{middle_three}{last_three}"
                    self.logger.info(f"Randomized UK phone: {original_phone} -> {modified_phone}")
                    return modified_phone
                elif phone_length == 11:
                    # Some UK numbers might be 11 digits
                    mobile_prefix = original_phone[:4]  # Keep mobile prefix
                    last_three = original_phone[-3:]  # Keep last 3 digits
                    middle_four = ''.join([str(random.randint(0, 9)) for _ in range(4)])
                    modified_phone = f"{mobile_prefix}{middle_four}{last_three}"
                    self.logger.info(f"Randomized UK phone (11-digit): {original_phone} -> {modified_phone}")
                    return modified_phone

            # Fallback: Use original 3-4-3 pattern for unknown countries or edge cases
            if phone_length >= 7:
                first_three = original_phone[:3]
                last_three = original_phone[-3:]
                middle_length = phone_length - 6  # Calculate middle section length
                middle_digits = ''.join([str(random.randint(0, 9)) for _ in range(middle_length)])
                modified_phone = f"{first_three}{middle_digits}{last_three}"

                self.logger.info(f"Randomized phone (fallback pattern): {original_phone} -> {modified_phone}")
                return modified_phone
            else:
                self.logger.warning(f"Phone number too short to randomize: {original_phone}")
                return original_phone

        except Exception as e:
            self.logger.error(f"Error randomizing phone number: {str(e)}")
            return original_phone


    def _apply_phone_modification(self, clean_phone):
        try:
            if not clean_phone or len(clean_phone) < 6:
                self.logger.warning(f"Phone number too short for modification: {clean_phone}")
                return clean_phone

            # Generate random number to determine strategy (0-99)
            rand_num = random.randint(0, 99)

            if rand_num < 50:  # 50% probability - Change center digits
                phone_len = len(clean_phone)
                if phone_len >= 8:  # Need at least 8 digits for center modification
                    # Calculate center position (skip first 2 and last 2 digits)
                    start_pos = 2
                    end_pos = phone_len - 2
                    center_len = min(4, end_pos - start_pos)  # Modify up to 4 center digits

                    if center_len > 0:
                        # Generate new random digits for center
                        new_center = ''.join([str(random.randint(0, 9)) for _ in range(center_len)])
                        modified_phone = clean_phone[:start_pos] + new_center + clean_phone[start_pos + center_len:]
                        self.logger.info(f"Applied center digits modification: {clean_phone} -> {modified_phone}")
                        return modified_phone

                # Fallback if phone too short for center modification
                self.logger.info(f"Phone too short for center modification, using original: {clean_phone}")
                return clean_phone

            elif rand_num < 75:  # 25% probability - Change last 2 digits
                if len(clean_phone) >= 3:  # Need at least 3 digits
                    new_last_two = ''.join([str(random.randint(0, 9)) for _ in range(2)])
                    modified_phone = clean_phone[:-2] + new_last_two
                    self.logger.info(f"Applied last 2 digits modification: {clean_phone} -> {modified_phone}")
                    return modified_phone
                else:
                    self.logger.info(f"Phone too short for last digits modification, using original: {clean_phone}")
                    return clean_phone

            else:  # 25% probability - Use original number
                self.logger.info(f"Using original phone number (no modification): {clean_phone}")
                return clean_phone

        except Exception as e:
            self.logger.error(f"Error applying phone modification: {str(e)}")
            return clean_phone



    def _get_phone_number_with_5sim(self, country):
        """Handle phone verification using 5sim service with enhanced retry logic"""
        try:
            # No proxy health checks needed - using simple extension-based proxy

            # Initialize 5sim manager
            fivesim_manager = FiveSimManager(self.logger)

            # Enhanced retry logic with new phone numbers
            max_phone_attempts = 2  # Try up to 2 different phone numbers

            for attempt in range(max_phone_attempts):
                self.logger.info(f"Phone verification attempt {attempt + 1}/{max_phone_attempts}")

                # Get phone number from 5sim
                self.logger.info("Requesting phone number from 5sim...")

                
                phone_number = fivesim_manager.get_phone_number_for_gmx(country)

                if not phone_number:
                    self.logger.error(f"Failed to get phone number from 5sim (attempt {attempt + 1})")
                    if attempt < max_phone_attempts - 1:
                        self.logger.info("Retrying with new phone number...")
                        sleep(uniform(5.0, 10.0))  # Wait before retry
                        continue
                    else:
                        return None
                else:
                    return phone_number

        except Exception as e:
            self.logger.error(f"Error in 5sim phone verification: {str(e)}")
            return False



    def _save_account_credentials(self, email):
        """Save account credentials to date-specific file with colon delimiter"""
        try:
            # Create credentials directory if it doesn't exist
            os.makedirs("credentials", exist_ok=True)

            # Get current date for filename (YYYY-MM-DD format)
            current_date = datetime.now().strftime("%Y-%m-%d")
            filename = f"credentials/gmx_accounts_{current_date}.txt"

            # Get phone number (remove country code if present)
            phone_number = getattr(self, 'phone_number', 'N/A')
            if phone_number and phone_number != 'N/A':
                phone_number = phone_number.replace("+44", "").replace("+", "")

            # Get other fields with defaults
            password = getattr(self, 'password', 'N/A')
            birthday = getattr(self, 'birthday', 'N/A')
            first_name = getattr(self, 'first_name', 'N/A')
            last_name = getattr(self, 'last_name', 'N/A')

            # Format: email:password:phone:birthday:first_name:last_name
            credentials = f"{email}:{password}:{phone_number}:{birthday}:{first_name}:{last_name}"

            # Save to date-specific file
            with open(filename, "a", encoding="utf-8") as f:
                f.write(f"{credentials}\n")

            self.logger.info(f"Saved credentials for {email} to {filename}")

        except Exception as e:
            self.logger.error(f"Error saving credentials: {str(e)}")



    def _save_phone_number(self):
        """Save used phone number to separate file"""
        try:
            # Create credentials directory if it doesn't exist
            os.makedirs("credentials", exist_ok=True)

            # Get phone number
            phone_number = getattr(self, 'phone_number', None)
            if phone_number:
                # Clean phone number
                clean_phone = phone_number.replace("+44", "").replace("+", "")

                # Save to separate phone numbers file
                with open("credentials/used_phone_numbers.txt", "a", encoding="utf-8") as f:
                    f.write(f"{clean_phone}\n")

                self.logger.info(f"Saved phone number: {clean_phone}")
            else:
                self.logger.warning("No phone number to save")

        except Exception as e:
            self.logger.error(f"Error saving phone number: {str(e)}")


    def get_window_size_info(self):
        """Get information about the current window size choice"""
        if hasattr(self._enhanced_driver, 'window_size_choice'):
            choice = self._enhanced_driver.window_size_choice
            width = getattr(self._enhanced_driver, 'window_width', None)
            height = getattr(self._enhanced_driver, 'window_height', None)
            
            self.logger.info(f"Window size choice: {choice}")
            if width and height:
                self.logger.info(f"Window dimensions: {width}x{height}")
                
            return {
                'choice': choice,
                'width': width,
                'height': height
            }
        else:
            self.logger.warning("Window size choice information not available")
            return None


    def _load_gmx_cookies(self):
        """Load predefined GMX cookies to bypass consent dialogs and improve session persistence.

        Returns:
            bool: True if cookies were loaded successfully, False otherwise
        """
        try:
            self.logger.info("Loading predefined GMX cookies...")

            # Predefined GMX cookies for consent bypass and session persistence
            gmx_cookies = [
                {
                    "name": "consentLevel",
                    "value": "3",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "cookieKID",
                    "value": "kid%40autoref%40www.gmx.com",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "cookiePartner",
                    "value": "kid%40autoref%40www.gmx.com",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "euconsent-bypass",
                    "value": "1",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "euconsent-v2",
                    "value": "CQWFlcAQWFlcAAcABBENB3FkAP_gAAAAAAYgJZIR1G7fbXFjeT53YfpkaIwX1dBr6sQhBgTAk2AFzJuQ8JwC12E6MATApqACERIAolRBIQMEHABUAFCAIIAFAADMIESUoAAKICBEABEQAAIQAAgKEgIAEAAIgEBFIhUAmBiA6dLkxciACIAAB0AYgoABCIABAAMBAEAIQBAAAIIAwygAAQBAAIIAAAAAARAIAABBQAAAIAEAAABgSBeAAuACgAKgAcAA8ACCAGQAagA8ACYAFUAN4AfgBCQCGAIgARwAmgBWgDAAGGAMsAc8A7gDvAHtAPsA_QCKAEYAI1ASIBJQC5gGKANoAbgA4gCHYEegSIAnYBQ4CjwFIgLYAXmAw2BkYGSAMzgawBrIDcwHjgSKCADAAHAAkACOAJwAg4BHACaAJWAVCAv8BiwDIR0DcABcAFAAVAA4ACAAF0AMgA1AB4AEwAKsAXABdADeAH6AQwBEACaAE4AKMAVoAwABhgDRAHPAO4A7wB7QD7AP2AigCLAEYgI6AkoBYgC5gF5AMUAbQA3ABxAEOgIvgR6BIgCdgFDgKPAWwAt0BeYDDYGRgZIAyoBlgDMwGsAOLAeOA-sB_YEAQJFDgCwADgALgAkACOAGgARwAywByADogIOAhABHACaAIQASsAqEBagC_wGLAMhAfsBG8CQhCAoAAsACgALgAagBVAC4AG8AYAA54B3AHeARQAlIBcwDFAG0AR6AyMB44D-wJFEABAAaABlgDkAI4AWIB-wEMwI3koDYACwAKAAcAB4AEwAKoAXIBDAEQAI4AUYArQBgADvAH4AXMAxQCHQEXwI9AkQBR4C2AF5wMjAyQBlgDWAIAgSKJACgALgBHAHcAQcAjgCVgF_gMWAfsBDMpApAAXABQAFQAOAAggBkAGgAPAAmABVAD9AIYAiABRgCtAGAANEAc4A74B9gH6ARYAjEBHAElALmAXkAxQBtADcAIdARfAj0CRAE7AKHAWwAvMBhsDIwMkAZZA1gDWQHjgP7AhmBDkCRRQAiABcAEgALgAjgCOAE4AMsAcgA7gCDgFiANeAdsA_4C1AGLAP2LQBgAagDAAHcAocBmYDxywAMAZYBHAEegAAA.f_wAAAAABVAgAAAA",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "idcc",
                    "value": "1",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "uiconsent",
                    "value": "{%22permissionFeature%22:[%22fullConsent%22]}",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                }
            ]

            # Add each cookie to the browser
            cookies_loaded = 0



            for cookie in gmx_cookies:
                try:
                    # Use the enhanced driver's browser for cookie operations
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'browser'):
                        # Use the enhanced driver's browser (SeleniumBase BaseCase)
                        if hasattr(self._enhanced_driver.browser, 'add_cookie'):
                            self._enhanced_driver.browser.add_cookie(cookie)
                        elif hasattr(self._enhanced_driver.browser, 'driver') and hasattr(self._enhanced_driver.browser.driver, 'add_cookie'):
                            self._enhanced_driver.browser.driver.add_cookie(cookie)
                        else:
                            self.logger.warning("No add_cookie method found on enhanced driver browser")
                            continue
                    # Fallback to original browser reference
                    elif hasattr(self.browser, 'add_cookie'):
                        self.browser.add_cookie(cookie)
                    elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'add_cookie'):
                        self.browser.driver.add_cookie(cookie)
                    else:
                        self.logger.warning("No add_cookie method found on browser")
                        continue

                    cookies_loaded += 1
                    self.logger.debug(f"Loaded cookie: {cookie['name']}")

                except Exception as cookie_error:
                    self.logger.warning(f"Failed to load cookie {cookie['name']}: {str(cookie_error)}")
                    continue

            self.logger.info(f"Successfully loaded {cookies_loaded}/{len(gmx_cookies)} GMX cookies")
            return cookies_loaded > 0

        except Exception as e:
            self.logger.error(f"Error loading GMX cookies: {str(e)}")
            return False

    
    def _type_text_humanlike(self, element, text):
        """
        Type text character by character with human-like delays to simulate natural typing.

        Args:
            element: WebElement to type into
            text: String to type
        """
        try:
            for char in text:
                element.send_keys(char)
                # Random delay between keystrokes (50-200ms, typical human typing speed)
                delay = uniform(0.05, 0.2)
                # Occasionally add longer pauses (simulating thinking or hesitation)
                if random.random() < 0.1:  # 10% chance of longer pause
                    delay += uniform(0.2, 0.5)
                sleep(delay)

        except Exception as e:
            self.logger.debug(f"Error in human-like typing: {str(e)}")
            # Fallback to regular send_keys if character-by-character fails
            element.send_keys(text)


    def _close_gmx_popup(self):
        """
        Close any popup that appears on GMX pages using the provided XPath selectors.

        Returns:
            bool: True if popup was successfully closed or no popup was found, False if there was an error
        """
        try:
            self.logger.debug("Checking for GMX popups to close...")

            # Define popup close button selectors in order of preference
            popup_close_selectors = [
                "(//button[contains(@class, 'icon-close')])[2]",
                "//button[@aria-label='Close layer' and contains(@class, 'icon-close')]",
                "//button[contains(@class, 'icon-close')]",
                "//button[@aria-label='Close']",
                "//button[contains(@title, 'Close')]"
            ]

            popup_closed = False

            for i, selector in enumerate(popup_close_selectors, 1):
                try:
                    self.logger.debug(f"Trying popup close selector {i}: {selector}")

                    # Find the popup close button
                    close_button = None
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        close_button = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            close_button = self.browser.find_xpath(selector)
                        except:
                            close_button = None

                    if close_button and close_button.is_displayed():
                        self.logger.info(f"Found popup close button with selector {i}: {selector}")

                        # Click the close button with human-like behavior
                        try:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(close_button)
                            else:
                                close_button.click()

                            self.logger.info("Successfully clicked popup close button")

                            # Wait for popup to close
                            sleep(uniform(1.0, 2.0))

                            popup_closed = True
                            break

                        except Exception as click_error:
                            self.logger.debug(f"Failed to click popup close button: {str(click_error)}")
                            continue

                except Exception as e:
                    self.logger.debug(f"Popup close selector {i} failed: {str(e)}")
                    continue

            if popup_closed:
                self.logger.info("✅ Popup successfully closed")
            else:
                self.logger.debug("No popup found or popup already closed")

            return True

        except Exception as e:
            self.logger.error(f"Error during popup closing: {str(e)}")
            # Don't fail the entire process if popup closing fails
            return True

    def _login_to_gmx(self):
        """Login to GMX account."""
        self.browser.go("https://www.gmx.com")
        sleep(uniform(2.0, 3.0))
        try:
            self.logger.info("Starting GMX login process")

            # Step 1: Click login button
            login_button_selectors = [
                "//a[@id='login-button']",
                "//a[@class='button button-login']",
                "//a[span[text()='Log in']]",
                "//a[contains(@class, 'login')]",
                "//button[contains(text(), 'Log in')]"
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        login_button = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        login_button = self.browser.find_xpath(selector)

                    if login_button and login_button.is_displayed():
                        self.logger.info(f"Found login button with selector: {selector}")
                        break
                except:
                    continue

            if not login_button:
                self.logger.error("Could not find login button")
                return False

            # Click login button with human-like behavior
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(login_button)
            else:
                login_button.click()

            self.logger.info("Clicked login button")


            # Step 2: Wait for and find email input field
            email_input_selectors = [
                "//input[@id='login-email']",
                "//input[@name='username']",
                "//input[@placeholder='Email address']",
                "//input[@type='email']",
                "//input[contains(@class, 'email')]"
            ]

            email_input = None
            # Wait a bit for the login form to load
            sleep(uniform(1.0, 2.0))

            for selector in email_input_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        email_input = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        email_input = self.browser.find_xpath(selector)

                    if email_input and email_input.is_displayed():
                        self.logger.info(f"Found email input field with selector: {selector}")
                        break
                except:
                    continue

            if not email_input:
                self.logger.error("Could not find email input field")
                return False

            # Step 3: Type email address with human-like behavior
            try:
                email_input.clear()
                sleep(uniform(0.5, 1.0))

                if hasattr(self.browser, 'human_type'):
                    self.browser.human_type(email_input, self.email)
                else:
                    self._type_text_humanlike(email_input, self.email)

                self.logger.info(f"Entered email: {self.email}")
                sleep(uniform(1.0, 2.0))

            except Exception as e:
                self.logger.error(f"Failed to enter email: {str(e)}")
                return False

            # Try to close any popup that might appear
            self._close_gmx_popup()

            # Step 4: Find and fill password field (should be present since email field is displayed)
            password_input_selectors = [
                "//input[@type='password']",
                "//input[@autocomplete='current-password']",
                "//input[@name='password' and contains(@class, 'login-input')]",
                "//input[@name='password']",
                "//input[contains(@class, 'password')]"
            ]

            password_input = None
            for selector in password_input_selectors:
                try:
                    password_input = self._find_element_silent(selector, 'xpath', timeout=3)
                    if password_input:
                        self.logger.info(f"Found password input field with selector: {selector}")
                        break
                except:
                    continue

            if not password_input:
                self.logger.error("Could not find password input field")
                return False

            # Type password with human-like behavior

            try:
                password_input.clear()
                sleep(uniform(0.5, 1.0))

                if hasattr(self.browser, 'human_type'):
                    self.browser.human_type(password_input, self.password)
                else:
                    self._type_text_humanlike(password_input, self.password)

                self.logger.info("Entered password")
                sleep(uniform(1.0, 2.0))

            except Exception as e:
                self.logger.error(f"Failed to enter password: {str(e)}")
                return False

            # Step 5: Click submit/login button (should be present since email field is displayed)
            submit_button_selectors = [
                "//button[@type='submit' and contains(@class, 'login-submit')]",
                "//button[span[text()='Log in']]",
                "//button[@class='btn btn-block login-submit']",
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//button[contains(text(), 'Sign in')]",
                "//button[contains(text(), 'Login')]"
            ]


            submit_button = None
            for selector in submit_button_selectors:
                try:
                    submit_button = self._find_element_silent(selector, 'xpath', timeout=3)
                    if submit_button and submit_button.is_displayed():
                        self.logger.info(f"Found submit button with selector: {selector}")
                        break
                except:
                    continue

            if not submit_button:
                self.logger.error("Could not find submit button")
                return False

            # Click submit button
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(submit_button)
            else:
                submit_button.click()

            self.logger.info("Clicked submit button")

            # Step 6: Wait for page to load and verify login success
            sleep(uniform(3.0, 5.0))

            # Check for successful login indicators
            try:
                # Wait for page to stabilize
                WebDriverWait(self.browser, 15).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )

                current_url = self.browser.current_url
                self.logger.info(f"Login completed, current URL: {current_url}")

                # Check for login success indicators
                success_indicators = [
                    "navigator-bs.gmx.com/mail?sid=" in current_url.lower(),
                    "navigator" in current_url.lower(),
                    "mail?sid" in current_url.lower()
                ]

                if any(success_indicators):
                    self.logger.info("Login appears successful based on URL")
                    return True
                else:
                    self.logger.warning("Login status unclear, checking for error indicators")

                    # Check for error messages
                    error_selectors = [
                        "//div[contains(@class, 'error')]",
                        "//div[contains(@class, 'alert')]",
                        "//span[contains(text(), 'incorrect')]",
                        "//span[contains(text(), 'invalid')]"
                    ]

                    for selector in error_selectors:
                        try:
                            error_element = self._find_element_silent(selector, 'xpath', timeout=2)
                            if error_element and error_element.is_displayed():
                                self.logger.error(f"Login error detected: {error_element.text}")
                                return False
                        except:
                            continue

                    # If no errors found, assume success
                    self.logger.info("No error indicators found, assuming login successful")
                    return True

            except TimeoutException:
                self.logger.warning("Page load timeout, but continuing...")
                return True

        except Exception as e:
            self.logger.error(f"Error during GMX login: {str(e)}")
            return False



    def _fill_step1_personal_info(self, user_data):
        """Fill Step 1: Personal Information.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            self.logger.info("Filling Step 1: Personal Information")

            # Wait for page to fully load
            self.logger.info("Waiting for page to fully load...")
            sleep(uniform(5.0, 9.0))

            # First name input selectors
            first_name_selectors = [
                '//input[@data-test="first-name-input"]',
                '//*[@id="given-name"]'
            ]

            # Try to fill first name using enhanced driver methods
            first_name_filled = False
            for selector in first_name_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.info(f"Found first name input with selector: {selector}")

                        element.clear()
                        sleep(uniform(0.5, 1.0))

                        # Use human-like typing
                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(element, user_data['first_name'])
                        else:
                            element.send_keys(user_data['first_name'])

                        self.logger.info(f"Filled first name: {user_data['first_name']}")
                        first_name_filled = True
                        break

                except Exception as e:
                    self.logger.debug(f"First name selector {selector} failed: {str(e)}")
                    continue

            if not first_name_filled:
                self.logger.error(f"Failed to fill first name field")
                return False

            sleep(uniform(0.5, 0.8))

            # Last name input selectors
            last_name_selectors = [
                'input[data-test="last-name-input"]',
                '//*[@id="family-name"]'
            ]

            # Try to fill last name
            last_name_filled = False
            for selector in last_name_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.info(f"Found last name input with selector: {selector}")
                        element.clear()
                        sleep(uniform(0.5, 1.0))

                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(element, user_data['last_name'])
                        else:
                            element.send_keys(user_data['last_name'])

                        self.logger.info(f"Filled last name: {user_data['last_name']}")
                        last_name_filled = True
                        break

                except Exception as e:
                    self.logger.debug(f"Last name selector {selector} failed: {str(e)}")
                    continue

            if not last_name_filled:
                self.logger.error("Failed to fill last name field")
                return False
            

            sleep(uniform(0.5, 0.8))
            # Try different selectors for birth date inputs
            day_selectors = [
                '//*[@id="bday-day"]',
                '//input[@id="bday-day"]'
            ]

            month_selectors = [
                '//*[@id="bday-month"]',
                'input[id="bday-month"]'
            ]

            year_selectors = [
                '//*[@id="bday-year"]',
                '//input[@id="bday-year"]'
            ]

            # Try to find the birth date inputs
            day_input = None
            for selector in day_selectors:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                else:
                    try:
                        element = self.browser.find_xpath(selector)
                    except:
                        element = None

                if element and element.is_displayed():
                    day_input = element
                    break

            month_input = None
            for selector in month_selectors:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                else:
                    try:
                        element = self.browser.find_xpath(selector)
                    except:
                        element = None
                if element and element.is_displayed():
                    month_input = element
                    break

            year_input = None
            for selector in year_selectors:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(selector)
                else:
                    try:
                        element = self.browser.find_xpath(selector)
                    except:
                        element = None

                if element and element.is_displayed():
                    year_input = element
                    break



            birth_parts = user_data['birth_date'].split('.')

            if day_input and month_input and year_input:
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(day_input, birth_parts[0])
                else:
                    day_input.send_keys(birth_parts[0])
                sleep(uniform(0.5, 0.8))
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(month_input, birth_parts[1])
                else:
                    month_input.send_keys(birth_parts[1])
                sleep(uniform(0.5, 0.8))
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(year_input, birth_parts[2])
                else:
                    year_input.send_keys(birth_parts[2])
                sleep(uniform(0.5, 0.8))
            else:
                self.logger.error("Could not find all birth date input fields")
                return False
            
            
            # Try to find and click the "Next" button to proceed to step 2
            next_button_selectors = [
                '/html/body/onereg-app/div/onereg-form/div/div/form/section/section[1]/onereg-initial-info/fieldset/onereg-progress-meter/div[4]/button[1]',
                '//button[@data-test="progress-meter-next"]'
            ]

            next_button_clicked = False
            for selector in next_button_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        self.logger.info("Clicked next button to proceed to step 2")
                        sleep(uniform(2.0, 4.0))  # Wait for page transition
                        next_button_clicked = True
                        break

                except Exception as e:
                    self.logger.debug(f"Next button selector {selector} failed: {str(e)}")
                    continue

            if not next_button_clicked:
                self.logger.warning("Could not find or click next button, but continuing...")

            return True

        except Exception as e:
            self.logger.error(f"Error in Step 1: {e}")
            return False


    def _fill_step2_email_selection(self, user_data):
        """Fill Step 2: Email Address Selection.

        Randomly chooses between custom email entry and email suggestions
        to make the automation appear more human-like and less predictable.

        Args:
            user_data: User data dictionary

        Returns:
            tuple: (success: bool, selected_email: str)
        """
        try:
            self.logger.info("Filling Step 2: Email Address Selection")
            sleep(uniform(2.0, 3.0))

            # Check for "no email suggestions available" conditions first
            no_suggestions_xpaths = [
                '//div[contains(@class, "onereg-suggestion-item-advanced__text") and contains(text(), "no suggestions")]',
                '//div[text()="There are no suggestions for your entry"]',
                '//onereg-suggestion-item-advanced[.//div[contains(text(), "no suggestions")] and .//div[contains(text(), "Choose your desired email")]]'
            ]

            for xpath in no_suggestions_xpaths:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(xpath)
                    else:
                        element = self._find_element_silent(xpath, 'xpath', timeout=2)

                    if element and element.is_displayed():
                        self.logger.warning("No email suggestions available detected - canceling registration and waiting")
                        self.logger.info("Waiting 10 seconds before next attempt...")
                        sleep(10)  
                        return False, ""

                except Exception as e:
                    self.logger.debug(f"Error checking no suggestions xpath {xpath}: {str(e)}")
                    continue



            # Randomize email selection approach to appear more human-like
            # Weight: 75% chance for suggestions (more typical user behavior)
            #         25% chance for custom entry (less common but still realistic)
            use_suggestions_first = random.random() < 0.85

            if use_suggestions_first:
                self.logger.info("Randomly selected: Try email suggestions first")

                # Try email suggestions first
                success, selected_email = self._try_email_suggestions()
                if success:
                    return True, selected_email

                # Fallback to custom email entry if suggestions fail
                self.logger.info("Email suggestions failed, falling back to custom entry")
                custom_email_result = self._try_custom_email_entry(user_data)
                if custom_email_result and custom_email_result != False:
                    if isinstance(custom_email_result, tuple) and len(custom_email_result) == 2:
                        success, selected_email = custom_email_result
                        if success:
                            self.logger.info(f"Custom email entry successful: {selected_email}")
                            return True, selected_email
            else:
                self.logger.info("Randomly selected: Try custom email entry first")

                # Try custom email entry first
                custom_email_result = self._try_custom_email_entry(user_data)
                if custom_email_result and custom_email_result != False:
                    if isinstance(custom_email_result, tuple) and len(custom_email_result) == 2:
                        success, selected_email = custom_email_result
                        if success:
                            self.logger.info(f"Custom email entry successful: {selected_email}")
                            return True, selected_email

                # Fallback to email suggestions if custom entry fails
                self.logger.info("Custom email entry failed, falling back to suggestions")
                success, selected_email = self._try_email_suggestions()
                if success:
                    return True, selected_email

            # If both methods fail
            self.logger.error("Both email selection methods failed")
            return False, ""

        except Exception as e:
            self.logger.error(f"Error in Step 2: {e}")
            return False, ""


    def _try_email_suggestions(self):
        """
        Try to select from email suggestions.

        Returns:
            tuple: (success: bool, selected_email: str)
        """
        try:
            # Email suggestion selectors
            email_suggestion_selectors = [
                '//div[contains(@class, "suggestion")]//div[contains(text(), "@gmx.com") or contains(text(), "@gmx.us")]',
                '/html/body/onereg-app/div/onereg-form/div/div/form/section/section[2]/onereg-alias-advanced/fieldset/onereg-progress-meter/div[3]/div[1]/onereg-alias-advanced-suggestions/div/onereg-suggestion-item-advanced'
            ]

            # Try to find and select a free email suggestion
            email_selected = False
            selected_email = ""

            for selector in email_suggestion_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        elements = self._enhanced_driver.find_xpath_all(selector)
                    else:
                        try:
                            elements = self.browser.find_xpath_all(selector)
                        except:
                            elements = None
                    if elements:
                        # Select the first available email suggestion
                        for element in elements:
                            if element.is_displayed() and "@gmx.com" in element.text or "gmx.us" in element.text:
                                selected_email = element.text.strip()
                                self.logger.info(f"Found email suggestion: {selected_email}")

                                # Click on the suggestion
                                if hasattr(self.browser, 'human_click_element'):
                                    self.browser.human_click_element(element)
                                else:
                                    element.click()

                                self.logger.info(f"Selected email: {selected_email}")
                                email_selected = True
                                break

                        if email_selected:
                            return True, selected_email

                except Exception as e:
                    self.logger.debug(f"Email suggestion selector {selector} failed: {str(e)}")
                    continue

            if not email_selected:
                self.logger.debug("Could not find or select any email suggestion")
                return False, ""

        except Exception as e:
            self.logger.error(f"Error in email suggestions: {str(e)}")
            return False, ""


    def _try_custom_email_entry(self, user_data):
        """
        Try to enter a custom email username before falling back to email suggestions.

        Args:
            user_data: User data dictionary containing username

        Returns:
            tuple: (success: bool, selected_email: str) or False if failed
        """
        try:
            self.logger.info("Attempting custom email entry")

            # Generate a username if not already in user_data
            if 'username' not in user_data or not user_data['username']:
                username = self.data_generator.generate_username(
                    user_data.get('first_name'),
                    user_data.get('last_name')
                )
            else:
                username = user_data['username']

            # Shorten the username to 8-12 characters for better email compatibility
            if len(username) > 12:
                # Keep the first 8-12 characters, randomly choosing the exact length
                target_length = random.randint(8, 12)
                username = username[:target_length]
                self.logger.info(f"Shortened username to {target_length} characters: {username}")
            elif len(username) < 6:
                # If username is too short, pad it with random numbers
                while len(username) < 6:
                    username += str(random.randint(0, 9))
                self.logger.info(f"Padded short username: {username}")

            # Ensure username always contains numbers
            import re
            if not re.search(r'\d', username):
                # If no numbers found, add 2-4 random numbers at the end
                num_digits = random.randint(2, 4)
                numbers = ''.join([str(random.randint(0, 9)) for _ in range(num_digits)])

                # If adding numbers would make username too long, remove some letters first
                if len(username) + num_digits > 12:
                    username = username[:12-num_digits]

                username += numbers
                self.logger.info(f"Added {num_digits} numbers to ensure username has digits: {username}")

            self.logger.info(f"Trying to enter custom username: {username}")

            # Email input field selectors (in order of preference)
            email_input_selectors = [
                "//input[@type='text' and @formcontrolname='alias']",
                "//input[@data-test='check-email-availability-email-input']",
                "//input[@class='pos-form-element pos-text-input email-alias-advanced-input__alias-text-input pos-floating-label ng-valid ng-touched ng-pristine']"
            ]

            # Try to find and fill the email input field
            email_input = None
            for selector in email_input_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        email_input = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        email_input = self._find_element_silent(selector, 'xpath', timeout=3)

                    if email_input and email_input.is_displayed():
                        self.logger.info(f"Found email input field with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Email input selector {selector} failed: {str(e)}")
                    continue

            if not email_input:
                self.logger.debug("No custom email input field found, falling back to suggestions")
                return False

            # Clear and enter the username
            try:
                email_input.clear()
                sleep(uniform(0.5, 1.0))

                # Type the username with human-like timing
                if hasattr(self.browser, 'human_type'):
                    self.browser.human_type(email_input, username)
                else:
                    # Implement character-by-character typing with realistic delays
                    self._type_text_humanlike(email_input, username)

                self.logger.info(f"Entered username: {username}")
                sleep(uniform(1.0, 2.0))

            except Exception as e:
                self.logger.error(f"Failed to enter username: {str(e)}")
                return False

            # Check button selectors (in order of preference)
            check_button_selectors = [
                "//button[@data-test='check-email-availability-check-button']",
                "//button[.//span[text()='Check']]",
                "//button[@type='button' and contains(@class, 'email-alias-advanced-input__check')]"
            ]

            # Try to find and click the check button
            check_button = None
            for selector in check_button_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        check_button = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        check_button = self._find_element_silent(selector, 'xpath', timeout=3)

                    if check_button and check_button.is_displayed():
                        self.logger.info(f"Found check button with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Check button selector {selector} failed: {str(e)}")
                    continue

            if not check_button:
                self.logger.error("No check button found")
                return False

            # Click the check button
            try:
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(check_button)
                else:
                    check_button.click()

                self.logger.info("Clicked check button")

                # Wait 5-8 seconds as specified
                wait_time = uniform(5.0, 8.0)
                self.logger.info(f"Waiting {wait_time:.1f} seconds for email availability check")
                sleep(wait_time)

                # Check if the custom email was accepted
                # This could be verified by checking for success indicators or absence of error messages
                custom_email = f"{username}@gmx.com"
                self.logger.info(f"Custom email entry completed: {custom_email}")

                return True, custom_email

            except Exception as e:
                self.logger.error(f"Failed to click check button: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error in custom email entry: {str(e)}")
            return False

    def _fill_step3_address(self, user_data):
        """Fill Step 3: Country, Region and Gender Selection.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            self.logger.info("Filling Step 3: Country, Region and Gender Selection")
            sleep(uniform(2.0, 3.0))

            # US states list for random selection
            us_states = [
                "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "DC", "FL",
                "GA", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME",
                "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH",
                "NJ", "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI",
                "SC", "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY"
            ]

            # Gender/salutation options
            gender_options = ["FEMALE", "MALE"]
            selected_gender = random.choice(gender_options)

            # Select gender/salutation radio button
            gender_filled = False
            try:
                gender_selector = f'//input[@value="{selected_gender}"]/parent::label'
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(gender_selector)
                else:
                    try:
                        element = self.browser.find_xpath(gender_selector)
                    except:
                        element = None


                if element:
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(element)
                    else:
                        element.click()
                else:
                   self.browser.execute_script("""
                        const input = document.querySelector('input[value="{}"]');
                        input.checked = true;
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('click', {{ bubbles: true }}));
                    """.format(selected_gender))

                gender_text = {"FEMALE": "Ms", "MALE": "Mr", "UNKNOWN": "Other"}[selected_gender]
                self.logger.info(f"Selected gender: {gender_text}")
                gender_filled = True
                sleep(uniform(0.4, 0.7))
                

            except Exception as e:
                self.logger.debug(f"Gender selection failed: {str(e)}")

            # Select country (United States of America)
            country_filled = False
            try:
                country_selector = '//select[@data-test="country-input"]'
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(country_selector)
                else:
                    try:
                        element = self.browser.find_xpath(country_selector)
                    except:
                        element = None

                if element and element.is_displayed():
                    # Select US option
                    select = Select(element)
                    select.select_by_value("US")

                    self.logger.info("Selected country: United States of America")
                    country_filled = True
                    sleep(uniform(1.0, 2.0))  # Wait for region dropdown to populate

            except Exception as e:
                self.logger.debug(f"Country selection failed: {str(e)}")

            # Select random US state from region dropdown
            region_filled = False
            if country_filled:  # Only try if country was selected successfully
                try:
                    region_selector = '//select[@data-test="region-input"]'
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(region_selector)
                    else:
                        try:
                            element = self.browser.find_xpath(region_selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        # Select random US state
                        selected_state = random.choice(us_states)
                        select = Select(element)
                        select.select_by_value(selected_state)

                        self.logger.info(f"Selected region: {selected_state}")
                        region_filled = True
                        sleep(uniform(0.5, 1.0))

                except Exception as e:
                    self.logger.debug(f"Region selection failed: {str(e)}")

            # Check if required fields were filled
            if not (gender_filled and country_filled and region_filled):
                self.logger.warning(f"Some required fields could not be filled, but continuing..., gender_filled: {gender_filled}, country_filled: {country_filled}, region_filled: {region_filled}")

            # Wait for next button to become enabled
            sleep(uniform(1.0, 2.0))

            # Try to find and click the "Next" button to proceed to step 2
            next_button_selectors = [
                '/html/body/onereg-app/div/onereg-form/div/div/form/section/section[6]/onereg-progress-meter/div[4]/button[1]',
                '(//button[@data-test="progress-meter-next"])[5]'
            ]

            next_button_clicked = False
            for selector in next_button_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        self.logger.info("Clicked next button to proceed to step 2")
                        sleep(uniform(2.0, 4.0))  # Wait for page transition
                        next_button_clicked = True
                        break

                except Exception as e:
                    self.logger.debug(f"Next button selector {selector} failed: {str(e)}")
                    continue

            if not next_button_clicked:
                self.logger.warning("Could not find or click next button, but continuing...")

            return True

        except Exception as e:
            self.logger.error(f"Error in Step 3: {e}")
            return False


    def _fill_step4_password(self, user_data):
        """Fill Step 4: Password.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            self.logger.info("Filling Step 4: Password")
            sleep(uniform(2.0, 3.0))




            # Password input selectors
            password_selectors = [
                '//input[@type="password"]'
            ]

            # Try to fill password
            password_filled = False
            for selector in password_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        elements = self._enhanced_driver.find_xpath_all(selector)
                    else:
                        try:
                            elements = self.browser.find_xpath_all(selector)
                        except Exception as e:
                            print(str(e))
                            elements = None
                    if elements:
                        for element in elements:
                            self.logger.info(f"Found password input with selector: {selector}")
                            element.clear()
                            sleep(uniform(0.5, 1.0))

                            if hasattr(self.browser, 'human_type_text'):
                                self.browser.human_type_text(element, user_data['password'])
                            else:
                                element.send_keys(user_data['password'])

                            self.logger.info("Password filled successfully")
                            password_filled = True
                        break

                except Exception as e:
                    self.logger.debug(f"Password selector {selector} failed: {str(e)}")
                    continue

            if not password_filled:
                self.logger.error("Failed to fill password field")
                return False
            # Try to find and click the "Next" button to proceed to step 2
            next_button_selectors = [
                '/html/body/onereg-app/div/onereg-form/div/div/form/section/section[7]/onereg-password-advanced/fieldset/onereg-progress-meter/div[4]/button[1]',
                '(//button[@data-test="progress-meter-next"])[6]'
            ]

            next_button_clicked = False
            for selector in next_button_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        self.logger.info("Clicked next button to proceed to step 2")
                        sleep(uniform(2.0, 4.0))  # Wait for page transition
                        next_button_clicked = True
                        break

                except Exception as e:
                    self.logger.debug(f"Next button selector {selector} failed: {str(e)}")
                    continue

            if not next_button_clicked:
                self.logger.warning("Could not find or click next button, but continuing...")

            return True

        except Exception as e:
            self.logger.error(f"Error in Step 4: {e}")
            return False


    def _fill_step5_phone_verification(self):
        """Fill Step 5: Phone Verification.

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            self.logger.info("Filling Step 5: Phone Verification")
            sleep(uniform(2.0, 3.0))
            country = random.choice(['england'])
            # Phone number handling logic
            phone_number = self._get_phone_number(country)
            if not phone_number:
                self.logger.error("Failed to get phone number")
                return False

            # Store phone number for later saving
            self.phone_number = phone_number

            try:
                phone_prefix_selector = '//select[@data-test="mobile-phone-prefix-input"]'
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(phone_prefix_selector)
                else:
                    try:
                        element = self.browser.find_xpath(phone_prefix_selector)
                    except:
                        element = None

                if element and element.is_displayed():
                    # Select US option
                    select = Select(element)
                    if country == 'england':
                        select.select_by_value("9: Object")
                    elif country == 'usa':
                        select.select_by_value("27: Object")
                    elif country == 'canada':
                        select.select_by_value("3: Object")
                    elif country == 'germany':
                        select.select_by_value("5: Object")

                    self.logger.info(f"Selected Phone : {country}")
                    sleep(uniform(1.0, 2.0))  # Wait for region dropdown to populate

            except Exception as e:
                self.logger.debug(f"Country selection failed: {str(e)}")


            # Phone number input selectors
            phone_selectors = [
                '//input[@type="tel"]',
                '//input[@id="mobile-phone"]'
            ]

            

            # Fill phone number
            phone_filled = False
            for selector in phone_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except Exception as e:
                            print(str(e))
                            element = None

                    if element and element.is_displayed():
                        element.clear()
                        sleep(uniform(0.5, 1.0))

                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(element, phone_number)
                        else:
                            element.send_keys(phone_number)

                        self.logger.info(f"Filled phone number: {phone_number}")
                        phone_filled = True
                        break

                except Exception as e:
                    self.logger.debug(f"Phone selector {selector} failed: {str(e)}")
                    continue

            if not phone_filled:
                self.logger.error("Failed to fill phone number field")
                return False

               
            next_button_selectors = [
                '/html/body/onereg-app/div/onereg-form/div/div/form/section/section[9]/onereg-password-recovery-advanced/fieldset/onereg-progress-meter/div[4]/button[1]',
                '(//button[@data-test="progress-meter-next"])[8]'
            ]

            next_button_clicked = False
            for selector in next_button_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        self.logger.info("Clicked next button to proceed to step 2")
                        sleep(uniform(2.0, 4.0))  # Wait for page transition
                        next_button_clicked = True
                        break

                except Exception as e:
                    self.logger.debug(f"Next button selector {selector} failed: {str(e)}")
                    continue

            if not next_button_clicked:
                self.logger.warning("Could not find or click next button, but continuing...")

            return True

        except Exception as e:
            self.logger.error(f"Error in Step 5: {e}")
            return False


    def _get_captcha_params(self):
        _,height = self._get_screen_res()
        window_info = self.get_window_size_info()
        screen_size = window_info.get("choice")
        if screen_size == "fullscreen" and height == 1440:
            captcha_bbox = (1114, 609, 1447, 871)  # (left, top, right, bottom)
            slide_button_coords = (1154, 831)  # Average from manual measurements
        elif screen_size == "fullscreen" and height == 1080:
            captcha_bbox = (751, 402, 1169, 730)  # (left, top, right, bottom)
            slide_button_coords = (805, 679)  # Average from manual measurements
        elif screen_size == "fullscreen" and height == 900:
            captcha_bbox = (634, 338, 966, 602)  # (left, top, right, bottom)
            slide_button_coords = (674,564)  # Average from manual measurements
        elif screen_size == "custom" and height == 1440:
            captcha_bbox = (529, 347, 862, 608)  # (left, top, right, bottom)
            slide_button_coords = (570, 570)  # Average from manual measurements
        elif screen_size == "custom" and height == 1080:
            captcha_bbox = (663, 433, 1075, 763)  # (left, top, right, bottom)
            slide_button_coords = (713, 712)  # Average from manual measurements
        elif screen_size == "custom" and height == 900:
            captcha_bbox =  (530, 346, 862, 608)  # (left, top, right, bottom)
            slide_button_coords = (573, 569)  # Average from manual measurements
        return captcha_bbox, slide_button_coords





    def _get_screen_res(self):
        self.screen_width, self.screen_height = pyautogui.size()
        return self.screen_width, self.screen_height



    def _wait_for_cloudflare_modal_appear(self, timeout=180, poll_interval=3):
        """
        Wait for the Cloudflare captcha modal to appear on the page.

        Args:
            timeout (int): Maximum time to wait in seconds (default: 60)
            poll_interval (float): Time between checks in seconds (default: 2)

        Returns:
            bool: True if modal appeared, False if timeout reached
        """
        try:
            self.logger.info("Monitoring page for Cloudflare captcha modal to appear...")

            start_time = time.time()
            modal_selector = "//div[contains(@class, 'cf-modal__wrap') and contains(@class, 'center')]"

            # Wait for modal to appear
            while time.time() - start_time < timeout:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        modal_element = self._enhanced_driver.find_xpath_silent(modal_selector)
                    else:
                        modal_element = self._find_element_silent(modal_selector, 'xpath', timeout=2)
                    if modal_element and modal_element.is_displayed():
                        elapsed_time = time.time() - start_time
                        self.logger.info(f"Cloudflare captcha modal appeared after {elapsed_time:.1f} seconds")
                        return True

                    # Modal not present yet, continue waiting
                    elapsed_time = time.time() - start_time
                    self.logger.debug(f"Cloudflare modal not yet visible after {elapsed_time:.1f}s - continuing to wait...")
                    sleep(poll_interval)


                except Exception as e:
                    # If we can't find the element, it's not there yet
                    self.logger.debug(f"Modal check error (not yet present): {str(e)}")
                    elapsed_time = time.time() - start_time
                    self.logger.debug(f"Continuing to wait for modal after {elapsed_time:.1f}s...")
                    sleep(poll_interval)

            # Timeout reached
            elapsed_time = time.time() - start_time
            self.logger.warning(f"Timeout reached ({timeout}s) - Cloudflare modal did not appear after {elapsed_time:.1f} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error waiting for Cloudflare modal to appear: {str(e)}")
            return False



    def _wait_for_cloudflare_modal_disappear(self, timeout=120, poll_interval=1):
        """
        Wait for the Cloudflare captcha modal to disappear from the page.

        Args:
            timeout (int): Maximum time to wait in seconds (default: 120)
            poll_interval (float): Time between checks in seconds (default: 2)

        Returns:
            bool: True if modal disappeared, False if timeout reached
        """
        sleep(uniform(1.0, 2.0))
        try:
            self.logger.info("Monitoring Cloudflare captcha modal for disnce...")

            start_time = time.time()
            modal_selector = "//div[contains(@class, 'cf-modal__wrap') and contains(@class, 'center')]"

            # First check if modal is present
            modal_present = False

            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    modal_element = self._enhanced_driver.find_xpath_silent(modal_selector)
                else:
                    modal_element = self._find_element_silent(modal_selector, 'xpath', timeout=2)

                if modal_element and modal_element.is_displayed():
                    modal_present = True
                    self.logger.info("Cloudflare captcha modal detected - waiting for disappearance...")
                else:
                    self.logger.info("No Cloudflare modal found - proceeding immediately")
                    return True

            except Exception as e:
                self.logger.debug(f"Error checking initial modal presence: {str(e)}")
                # If we can't detect the modal, assume it's not there
                return True

            if modal_present:
                while time.time() - start_time < timeout:
                    try:
                        # Check if modal still exists and is displayed
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            modal_element = self._enhanced_driver.find_xpath_silent(modal_selector)
                        else:
                            modal_element = self._find_element_silent(modal_selector, 'xpath', timeout=2)

                        if not modal_element or not modal_element.is_displayed():
                            elapsed_time = time.time() - start_time
                            self.logger.info(f"Captcha modal disappeared after {elapsed_time:.1f} seconds")
                            return True

                        # Modal still present, continue waiting
                        elapsed_time = time.time() - start_time
                        self.logger.debug(f" still present after {elapsed_time:.1f}s - continuing to wait...")
                        sleep(poll_interval)

                    except Exception as e:
                        # If we can't find the element, it might have disappeared
                        self.logger.debug(f"Modal check error (likely disappeared): {str(e)}")
                        elapsed_time = time.time() - start_time
                        self.logger.info(f"Cloudflare modal likely disappeared after {elapsed_time:.1f} seconds")
                        return True

                # Timeout reached
                self.logger.warning(f"Timeout reached ({timeout}s) - Cloudflare modal still present")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error waiting for Cloudflare modal to disappear: {str(e)}")
            return False

    def _verify_captcha(self, driver):
        """
        Verify if captcha is solved by checking button state and captcha completion status
        Returns True if captcha is solved, False otherwise
        """
        #sleep(uniform(2.0, 3.0))
        try:
            self.logger.info("Verifying captcha completion status...")

            # Check if the "Agree and continue" button is enabled (captcha solved)
            button_selector = '//button[@data-test="create-mailbox-create-button"]'
            if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                button = self._enhanced_driver.find_xpath_silent(button_selector)
            else:
                try:
                    button = self.browser.find_xpath(button_selector)
                except:
                    button = None
            if button:
                # Check if button is no longer disabled
                is_disabled = button.get_attribute('disabled')
                if not is_disabled:
                    self.logger.info("✅ Button is enabled - captcha appears to be solved")

                    # Double-check by looking for the solved captcha indicator
                    captcha_success_selectors = [
                        '//div[@aria-checked="true"]',  # Checkbox marked as checked
                        '//div[contains(text(), "CaptchaFox checkbox is checked. You are a human.")]',
                        '//div[text()="CaptchaFox checkbox is checked. You are a human."]'
                    ]

                    for selector in captcha_success_selectors:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            success_element = self._enhanced_driver.find_xpath_silent(button_selector)
                        else:
                            try:
                                success_element = self.browser.find_xpath(button_selector)
                            except:
                                success_element = None
                        if success_element:
                            self.logger.info(f"✅ Captcha success indicator found: {selector}")
                            return True

                else:
                    self.logger.info("❌ Button still disabled - captcha not solved yet")
                    return False
            else:
                self.logger.warning("Could not find the submit button")
                return False

        except Exception as e:
            self.logger.error(f"Error verifying captcha: {e}")
            return False


    def _check_ip_compatibility(self):
        """
        Check if current IP is compatible with GMX by testing navigation to signup page.

        Returns:
            bool: True if IP is compatible (clean URL), False if blocked/restricted
        """
        try:
            self.logger.info("Checking IP compatibility with GMX...")

            # Navigate to GMX signup page to test IP compatibility
            try:
                self.browser.go("https://signup.gmx.com")
                sleep(uniform(2.0, 4.0))  # Wait for page to load and any redirects

            except Exception as nav_error:
                self.logger.error(f"Failed to navigate to GMX signup page: {str(nav_error)}")
                return False

            # Get current URL after navigation
            try:
                current_url = self.browser.this_url()
                self.logger.info(f"IP compatibility check - Current URL: {current_url}")

            except Exception as url_error:
                self.logger.error(f"Failed to get current URL: {str(url_error)}")
                return False

            # Check for IP restriction indicators
            if "reject" in current_url.lower():
                self.logger.warning("❌ IP compatibility check failed - 'reject' detected in URL")
                self.logger.warning("Current IP appears to be blocked by GMX")
                return False

            if "support" in current_url.lower():
                self.logger.warning("❌ IP compatibility check failed - 'support' detected in URL")
                self.logger.warning("Current IP appears to be restricted by GMX")
                return False

            # Additional checks for other restriction patterns
            restriction_patterns = ["blocked", "restricted", "unavailable", "error"]
            for pattern in restriction_patterns:
                if pattern in current_url.lower():
                    self.logger.warning(f"❌ IP compatibility check failed - '{pattern}' detected in URL")
                    return False

            # If we reach here, URL appears clean
            self.logger.info("✅ IP compatibility check passed - URL appears clean")
            self.logger.info("Current IP is compatible with GMX")
            return True

        except Exception as e:
            self.logger.error(f"Error during IP compatibility check: {str(e)}")
            # In case of error, assume IP might be problematic
            return False

    def _enable_pop_imap_smtp(self):
        """Enable POP/IMAP/SMTP protocols for GMX account"""
        # Check IP compatibility before attempting login
        if not self._check_ip_compatibility():
            self.logger.error("IP compatibility check failed - skipping login attempt")
            return False

        self.logger.info("IP compatibility confirmed - proceeding with GMX login...")
        successful_login = self._login_to_gmx()
        if successful_login:
            try:
                self.logger.info("Starting POP/IMAP/SMTP activation process...")

                # Step 1: Extract session ID from current URL
                current_url = self.browser.this_url()
                self.logger.info(f"Current URL: {current_url}")

                if "?sid=" not in current_url:
                    self.logger.error("No session ID found in current URL")
                    return False

                # Extract session ID after ?sid=
                session_id = current_url.split("?sid=")[1].split("&")[0]
                self.logger.info(f"Extracted session ID: {session_id[:20]}...")

                # Step 2: Navigate to mail settings page
                base_url = current_url.split("?sid=")[0].replace("/mail", "")
                settings_url = f"{base_url}/mail_settings?sid={session_id}"
                self.logger.info(f"Navigating to settings: {settings_url}")

                self.browser.go(settings_url)

                sleep(uniform(5000.0, 7000.0))



                sleep(uniform(5.5, 8.5))

                # Step 3: Switch to mail_settings iframe
                self.logger.info("Switching to mail_settings iframe...")
                iframe_selectors = [
                    '//iframe[@name="mail_settings"]',
                    '//iframe[@id="thirdPartyFrame_mail_settings"]',
                    '//iframe[@data-test="third-party-frame_mail_settings"]'
                ]

                iframe_found = False
                for selector in iframe_selectors:
                    try:
                        iframe = self.browser.find_xpath(selector)
                        if iframe:
                            self.browser.switch_to.frame(iframe)
                            iframe_found = True
                            self.logger.info(f"Successfully switched to iframe: {selector}")
                            break
                    except Exception as e:
                        self.logger.debug(f"Failed to switch to iframe {selector}: {str(e)}")
                        continue

                if not iframe_found:
                    self.logger.error("Could not find or switch to mail_settings iframe")
                    return False

                # Step 5: Activate the checkbox using JavaScript
                self.logger.info("Activating POP/IMAP/SMTP checkbox...")
                try:
                    checkbox_result = self.browser.execute_script("""
                        var checkbox = document.querySelector('input[name="popImapChapter:pop3ImapSmtpCheckBox"]');
                        if (checkbox && !checkbox.checked) {
                            checkbox.click();
                            return true;
                        }
                        return checkbox ? checkbox.checked : false;
                    """)

                    if checkbox_result:
                        self.logger.info("POP/IMAP/SMTP checkbox activated successfully")
                    else:
                        self.logger.error("Failed to activate POP/IMAP/SMTP checkbox")
                        return False
                except Exception as e:
                    self.logger.error(f"Error activating checkbox: {str(e)}")
                    return False

                sleep(uniform(4.0, 6.0))

                # Step 6: Click save button
                self.logger.info("Clicking save button...")
                save_clicked = False

                save_selectors = [
                    'button[name="popImapChapter:submitButtons:save"]',
                    'button[data-webdriver="Settings:Save"]'
                ]

                for selector in save_selectors:
                    try:
                        result = self.browser.execute_script(f"""
                            var button = document.querySelector('{selector}');
                            if (button) {{
                                button.click();
                                return true;
                            }}
                            return false;
                        """)
                        if result:
                            save_clicked = True
                            self.logger.info(f"Save button clicked: {selector}")
                            break
                    except Exception as e:
                        self.logger.debug(f"Failed to click save button {selector}: {str(e)}")
                        continue

                # Fallback: Find Save button by text content
                if not save_clicked:
                    try:
                        result = self.browser.execute_script("""
                            var buttons = Array.from(document.querySelectorAll('button'));
                            var saveButton = buttons.find(btn => btn.textContent.trim() === 'Save');
                            if (saveButton) {
                                saveButton.click();
                                return true;
                            }
                            return false;
                        """)
                        if result:
                            save_clicked = True
                            self.logger.info("Save button clicked by text content")
                    except Exception as e:
                        self.logger.debug(f"Text-based save button click failed: {str(e)}")

                if not save_clicked:
                    self.logger.error("Failed to click save button")
                    return False

                # Step 7: Wait for captcha to appear and solve it
                self.logger.info("Waiting for captcha to appear...")
                sleep(uniform(5.0, 7.0))

                return self._solve_settings_captcha()

            except Exception as e:
                self.logger.error(f"Error in _enable_pop_imap_smtp: {str(e)}")
                return False
            finally:
                # Switch back to default content
                try:
                    self.browser.switch_to.default_content()
                except:
                    pass

    def _solve_settings_captcha(self):
        """Solve captcha that appears after saving POP/IMAP/SMTP settings"""
        try:
            self.logger.info("Starting settings captcha solving process...")

            # Step 1: Extract captcha image
            captcha_image_path = self._extract_settings_captcha_image()
            if not captcha_image_path:
                self.logger.error("Failed to extract captcha image")
                return False

            # Step 2: Solve captcha using solvecaptcha
            captcha_solution = self._solve_captcha_with_solvecaptcha(captcha_image_path)
            if not captcha_solution:
                self.logger.error("Failed to solve captcha")
                return False

            # Step 3: Input captcha solution
            if not self._input_captcha_solution(captcha_solution):
                self.logger.error("Failed to input captcha solution")
                return False

            # Step 4: Submit captcha
            if not self._submit_captcha_form():
                self.logger.error("Failed to submit captcha form")
                return False

            # Step 5: Wait and verify settings are enabled
            sleep(5.0)
            self.browser.refresh()
            sleep(uniform(3.0, 5.0))

            return self._verify_pop_imap_smtp_enabled()

        except Exception as e:
            self.logger.error(f"Error in _solve_settings_captcha: {str(e)}")
            return False

    def _extract_settings_captcha_image(self):
        """Extract captcha image from settings page"""
        try:
            self.logger.info("Extracting captcha image...")

            # Captcha image selectors
            image_selectors = [
                '//img[@class="captcha_image"]',
                '//div[@class="captcha_image-container"]//img',
                '//div[@class="captcha_image-container"]/img[@class="captcha_image"]'
            ]

            captcha_img = None
            for selector in image_selectors:
                try:
                    captcha_img = self._find_element_silent(selector, 'xpath', timeout=5)
                    if captcha_img:
                        self.logger.info(f"Found captcha image with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {str(e)}")
                    continue

            if not captcha_img:
                self.logger.error("Could not find captcha image element")
                return None

            # Get image source
            img_src = captcha_img.get_attribute('src')
            if not img_src or img_src.strip() == '':
                self.logger.error("Captcha image has no src attribute")
                return None

            self.logger.info(f"Found captcha image with src: {img_src[:100]}...")

            # Handle data URLs (base64 encoded images)
            if img_src.startswith('data:image'):
                try:
                    # Extract base64 data
                    base64_data = img_src.split(',')[1]
                    image_data = base64.b64decode(base64_data)

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(image_data)
                    temp_file.close()

                    self.logger.info(f"Captcha image saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error processing base64 image: {str(e)}")
                    return None

            # Handle regular URLs
            else:
                try:
                    response = requests.get(img_src, timeout=10)
                    response.raise_for_status()

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(response.content)
                    temp_file.close()

                    self.logger.info(f"Captcha image downloaded and saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error downloading captcha image: {str(e)}")
                    return None

        except Exception as e:
            self.logger.error(f"Error extracting captcha image: {str(e)}")
            return None

    def _solve_captcha_with_solvecaptcha(self, image_path):
        """Solve captcha using solvecaptcha library"""
        try:
            self.logger.info("Solving captcha with solvecaptcha...")

            # Import solvecaptcha
            try:
                from solvecaptcha import SolveCaptcha
            except ImportError:
                self.logger.error("solvecaptcha library not installed. Install with: pip install solvecaptcha")
                return None

            # Initialize solver with API key
            api_key = "6d471eb76317bea61bc7fc56bc18d1e6"  # Using the same key from captcha_solver.py
            solver = SolveCaptcha(api_key)

            # Solve normal captcha
            with open(image_path, 'rb') as captcha_file:
                result = solver.normal_captcha(captcha_file)

            if result and 'code' in result:
                solution = result['code']
                self.logger.info(f"Captcha solved: {solution}")
                return solution
            else:
                self.logger.error(f"Solvecaptcha failed: {result}")
                return None

        except Exception as e:
            self.logger.error(f"Error solving captcha with solvecaptcha: {str(e)}")
            return None
        finally:
            # Clean up temporary image file
            try:
                if image_path and os.path.exists(image_path):
                    os.unlink(image_path)
            except Exception as e:
                self.logger.warning(f"Could not clean up temporary file {image_path}: {str(e)}")

    def _input_captcha_solution(self, solution):
        """Input captcha solution into the form"""
        try:
            self.logger.info(f"Inputting captcha solution: {solution}")

            # Captcha input selectors
            input_selectors = [
                '//input[@class="captcha_input form-element form-element-textfield textfield"]',
                '//input[@data-webdriver="captchaTextField"]',
                '//input[@placeholder="Enter the code shown"]',
                '//input[contains(@class, "captcha_input")]'
            ]

            captcha_input = None
            for selector in input_selectors:
                try:
                    captcha_input = self._find_element_silent(selector, 'xpath', timeout=5)
                    if captcha_input:
                        self.logger.info(f"Found captcha input with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Input selector {selector} failed: {str(e)}")
                    continue

            if not captcha_input:
                self.logger.error("Could not find captcha input field")
                return False

            # Clear and input solution
            captcha_input.clear()
            sleep(uniform(0.5, 1.0))

            # Type solution character by character for human-like behavior
            for char in solution:
                captcha_input.send_keys(char)
                sleep(uniform(0.1, 0.3))

            self.logger.info("Captcha solution inputted successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error inputting captcha solution: {str(e)}")
            return False

    def _submit_captcha_form(self):
        """Submit the captcha form"""
        try:
            self.logger.info("Submitting captcha form...")

            # Submit button selectors
            submit_selectors = [
                '//button[@data-webdriver="ok"]',
                '//button[@name="chapter:chapter_body:bottomButtons:container:bottomButtons_body:ok"]',
                '//button[normalize-space(text())="Continue"]'
            ]

            submit_clicked = False
            for selector in submit_selectors:
                try:
                    submit_button = self._find_element_silent(selector, 'xpath', timeout=5)
                    if submit_button:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(submit_button)
                        else:
                            submit_button.click()
                        submit_clicked = True
                        self.logger.info(f"Submit button clicked: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Submit selector {selector} failed: {str(e)}")
                    continue

            if not submit_clicked:
                self.logger.error("Failed to click submit button")
                return False

            self.logger.info("Captcha form submitted successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error submitting captcha form: {str(e)}")
            return False



    def _verify_pop_imap_smtp_enabled(self):
        """Verify that POP/IMAP/SMTP is enabled after settings change"""
        try:
            self.logger.info("Verifying POP/IMAP/SMTP is enabled...")

            # Switch back to iframe if needed
            iframe_selectors = [
                'iframe[name="mail_settings"]',
                'iframe[id="thirdPartyFrame_mail_settings"]',
                'iframe[data-test="third-party-frame_mail_settings"]'
            ]

            for selector in iframe_selectors:
                try:
                    iframe = self._find_element_silent(selector, 'css', timeout=10)
                    if iframe:
                        self.browser.switch_to.frame(iframe)
                        self.logger.info(f"Switched back to iframe: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Failed to switch to iframe {selector}: {str(e)}")
                    continue

            sleep(uniform(2.0, 3.0))

            # Check if checkbox is enabled
            try:
                is_enabled = self.browser.execute_script("""
                    var checkbox = document.querySelector('input[name="popImapChapter:pop3ImapSmtpCheckBox"]');
                    return checkbox ? checkbox.checked : false;
                """)

                if is_enabled:
                    self.logger.info("✅ POP/IMAP/SMTP is successfully enabled!")
                    return True
                else:
                    self.logger.error("❌ POP/IMAP/SMTP is not enabled")
                    return False

            except Exception as e:
                self.logger.error(f"Error checking POP/IMAP/SMTP status: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error verifying POP/IMAP/SMTP status: {str(e)}")
            return False
        finally:
            # Switch back to default content
            try:
                self.browser.switch_to.default_content()
            except:
                pass



    def _verify_account_creation(self, email=None):
        """
        Verify if account creation is successful by checking for success indicators.

        Args:
            email (str, optional): The email address that was created

        Returns:
            tuple: (success: bool, failure_type: str)
            failure_type can be: 'success', 'abuse', 'other'
        """

        try:
            self.logger.info("Verifying account creation status...")

            # Get current URL
            current_url = self.browser.this_url()
            self.logger.info(f"Current URL: {current_url}")

            # Check for failure indicators first

            # 1. Check if "abuse" is in URL - indicates account blocked
            if "abuse" in current_url.lower():
                self.logger.error("❌ Account creation failed - 'abuse' detected in URL")
                return False, "abuse"

            # 2. Check for account blocked message
            blocked_indicators = [
                '//p[contains(text(), "blocked your account.")]',
                '//span[@class="headline" and contains(., "Please") and contains(., "contact us!")]'
            ]

            for selector in blocked_indicators:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(selector)
                else:
                    try:
                        element = self.browser.find_xpath(selector)
                    except:
                        element = None
                if element:
                    self.logger.error(f"❌ Account creation failed - blocked account detected: {selector}")
                    return False, "abuse"

            # Check for success indicators

            # 1. Check if "welcome" is in URL - indicates success
            if "welcome" in current_url.lower():
                self.logger.info("✅ 'welcome' found in URL - account creation successful")

                # Double-check with success page elements
                success_indicators = [
                    '//h1[contains(text(), "Your GMX mailbox is currently being prepared for you")]',
                    '//h1[contains(text(), "Almost done! Your new mailbox is already waiting")]',
                    '//button[contains(text(), "Activate your account now")]',
                    '//section[@class="success-webapps-animation-container"]'
                ]

                for selector in success_indicators:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None
                    if element:
                        self.logger.info(f"✅ Success indicator confirmed: {selector}")
                        # Log successful user-agent when success is confirmed
                        self.log_successful_user_agent(email)
                        return True, "success"

                # If welcome in URL but no success elements, still consider successful
                self.logger.info("✅ Welcome URL detected - assuming account creation successful")
                # Log successful user-agent for welcome URL detection
                self.log_successful_user_agent(email)
                return True, "success"

            # If no clear success or failure indicators, check page content
            self.logger.warning("No clear success/failure indicators found - checking page content")

            # Look for any success elements without URL check
            success_selectors = [
                '//h1[contains(text(), "Your GMX mailbox is currently being prepared for you")]',
                '//h1[contains(text(), "Almost done! Your new mailbox is already waiting")]',
                '//button[contains(text(), "Activate your account now")]',
                '//section[@class="success-webapps-animation-container"]'
            ]

            for selector in success_selectors:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(selector)
                else:
                    try:
                        element = self.browser.find_xpath(selector)
                    except:
                        element = None
                if element:
                    self.logger.info(f"✅ Success element found: {selector}")
                    # Log successful user-agent when success element is found
                    self.log_successful_user_agent(email)
                    return True, "success"

            # If we reach here, no success indicators found
            self.logger.warning("❌ No success indicators found - account creation may have failed")
            return False, "other"

        except Exception as e:
            self.logger.error(f"Error verifying account creation: {e}")
            return False, "other"



    

    def _create_single_account(self):
        """
        Create a single GMX account with comprehensive error handling.

        Returns:
            tuple: (success: bool, failure_type: str)
            failure_type can be: 'success', 'reject', 'no_suggestions', 'abuse', 'other'
        """
        try:
            # Generate new user data for account creation
            user_data = self.data_generator.generate_user_data()
            self.logger.info(f"Generated user data for: {user_data['username']}")

            # Set instance variables for compatibility with existing methods
            self.first_name = user_data["first_name"]
            self.last_name = user_data["last_name"]
            self.username = user_data["username"]
            self.birthday = user_data["birth_date"]
            self.password = user_data["password"]

            # Setup browser session
            if not self._setup_browser_session(action_type="create"):
                self.logger.error("Failed to setup browser session")
                return False, "other"

            # Check for IP restriction/reject page
            current_url = self.browser.this_url()
            if "reject" in current_url or "support" in current_url:
                self.logger.warning("We are on the reject page!")
                self._cleanup_session(cleanup_type="reject")
                return False, "reject"

            # Execute registration steps
            selected_email = self._execute_registration_steps()
            if not selected_email:
                # Check if failure was due to no email suggestions
                if self._check_no_suggestions_failure():
                    self.logger.error("Registration failed - no email suggestions available")
                    self._cleanup_session(cleanup_type="registration_failed")
                    return False, "no_suggestions"
                else:
                    self.logger.error("Registration steps failed")
                    self._cleanup_session(cleanup_type="registration_failed")
                    return False, "other"

            # Handle captcha solving
            captcha_solved = self._handle_captcha_solving()
            if not captcha_solved:
                self.logger.error("Failed to solve captcha")
                self._cleanup_session(cleanup_type="captcha_failed")
                return False, "other"

            # Finalize account creation
            account_created = self._finalize_account_creation(selected_email)
            if account_created:
                self.logger.info("Account created successfully")
                sleep(random.uniform(5.0, 8.0))
                # Mark proxy as successfully used
                if self.proxy_manager and hasattr(self, 'current_proxy') and self.current_proxy:
                    self.proxy_manager.mark_proxy_used(self.current_proxy['id'], success=True)

                self._cleanup_session(cleanup_type="success")
                return True, "success"
            else:
                # Use the failure type from verification if available
                failure_type = getattr(self, '_last_verification_failure_type', 'other')
                self.logger.error(f"Failed to finalize account creation (Type: {failure_type})")

                # Mark proxy as failed
                if self.proxy_manager and hasattr(self, 'current_proxy') and self.current_proxy:
                    self.proxy_manager.mark_proxy_used(self.current_proxy['id'], success=False)
                self._cleanup_session(cleanup_type="finalization_failed")
                return False, failure_type

        except Exception as e:
            self.logger.error(f"Unexpected error during account creation: {str(e)}")
            # Mark proxy as failed
            if self.proxy_manager and hasattr(self, 'current_proxy') and self.current_proxy:
                self.proxy_manager.mark_proxy_used(self.current_proxy['id'], success=False)
            self._cleanup_session(cleanup_type="error")
            return False, "other"

    def _check_no_suggestions_failure(self):
        """
        Check if the current failure is due to no email suggestions being available.

        Returns:
            bool: True if failure is due to no suggestions, False otherwise
        """
        try:
            no_suggestions_xpaths = [
                '//div[contains(@class, "onereg-suggestion-item-advanced__text") and contains(text(), "no suggestions")]',
                '//div[text()="There are no suggestions for your entry"]',
                '//onereg-suggestion-item-advanced[.//div[contains(text(), "no suggestions")] and .//div[contains(text(), "Choose your desired email")]]'
            ]

            for xpath in no_suggestions_xpaths:
                try:
                    element = self._find_element_silent(xpath)
                    if element:
                        self.logger.debug(f"No suggestions element found: {xpath}")
                        return True
                except Exception as e:
                    self.logger.debug(f"Error checking no suggestions xpath {xpath}: {str(e)}")
                    continue

            return False
        except Exception as e:
            self.logger.error(f"Error checking for no suggestions failure: {str(e)}")
            return False

    def _setup_browser_session(self, action_type="create"):
        """
        Setup browser session and navigate to GMX signup page.

        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            # Rotate proxy before creating browser session
            if self.proxy_manager:
                selected_proxy = self.proxy_manager.rotate_proxy(strategy="random")
                if selected_proxy:
                    self.logger.info(f"Rotated to proxy: {selected_proxy['id']}")
                    # Store current proxy for later usage tracking
                    self.current_proxy = selected_proxy
                else:
                    self.logger.warning("No available proxies found, proceeding with default configuration")
                    self.current_proxy = None
            else:
                self.current_proxy = None

            # Initialize browser driver
            driver_instance = Driver(self.first_name, self.last_name, self.birthday, self.password, self.ac)
            self._setup_driver_references(driver_instance)


            # Navigate to GMX signup page
            #self.browser.go("https://www.browserscan.net/")
            self.browser.go("https://gmx.com")
            self._load_gmx_cookies()

            # Wait for page to stabilize
            #sleep(uniform(4000.0, 5000.5))
            if action_type == "create":
                self.browser.go("https://signup.gmx.com")

            sleep(1)

            # Log current URL for debugging
            current_url = self.browser.this_url()
            self.logger.info(f"DEBUG: Current URL after navigation: {current_url}")
            self.logger.info(f"DEBUG: Actions passed to script: {self.actions}")

            return True

        except Exception as e:
            if "ERR_PROXY_CONNECTION_FAILED" in str(e) or "net" in str(e) or "ERR_CONNECTION_RESET" in str(e):
                self.logger.error(f"Network connection error: {str(e)}")
                return False
            else:
                self.logger.error(f"Browser setup error: {str(e)}")
                return False

    def _execute_registration_steps(self):
        """
        Execute the 5-step GMX registration process.

        Args:
            user_data: Dictionary containing user registration data

        Returns:
            str: Selected email address if successful, None if failed
        """
        try:
            # Prepare user data for registration (ensure compatibility with existing methods)
            registration_data = {
                'first_name': self.first_name,
                'last_name': self.last_name,
                'username': self.username,
                'password': self.password,
                'birth_date': self.birthday
            }

            # Step 1: Personal Information
            if not self._fill_step1_personal_info(registration_data):
                self.logger.error("Step 1 (Personal Info) failed")
                return None
            self.logger.info("Step 1 completed successfully")

            # Step 2: Email Selection
            success, selected_email = self._fill_step2_email_selection(registration_data)
            if not success:
                self.logger.error("Step 2 (Email Selection) failed")
                return None
            self.logger.info(f"Step 2 completed successfully, email: {selected_email}")

            # Step 3: Address
            if not self._fill_step3_address(registration_data):
                self.logger.error("Step 3 (Address) failed")
                return None
            self.logger.info("Step 3 completed successfully")

            # Step 4: Password
            if not self._fill_step4_password(registration_data):
                self.logger.error("Step 4 (Password) failed")
                return None
            self.logger.info("Step 4 completed successfully")

            # Step 5: Phone Verification
            if not self._fill_step5_phone_verification():
                self.logger.error("Step 5 (Phone Verification) failed")
                return None
            self.logger.info("Step 5 completed successfully - GMX account created!")

            return selected_email

        except Exception as e:
            self.logger.error(f"Error during GMX registration steps: {str(e)}")
            return None
        


    def _handle_captcha_solving(self):
        """
        Handle captcha detection and solving with AI integration.

        Returns:
            bool: True if captcha solved successfully, False otherwise
        """
        try:
            # Look for captcha element
            element = self.browser.find_xpath('//div[@id="cf-pulse"]')
            if not element:
                self.logger.info("No captcha element found, proceeding without captcha solving")
                return True

            # Click captcha element
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(element)
            else:
                element.click()

            self._wait_for_cloudflare_modal_appear()


            sleep(uniform(2.0, 3.0))  # Wait for captcha to load

            captcha_bbox, slide_button_coords = self._get_captcha_params()

            # First attempt at solving captcha
            self.logger.info("Attempting to solve captcha with AI (attempt 1/2)")
            ai_solved = solve_captcha_automatically(
                captcha_bbox,
                slide_button_coords,
                objects="two main objects",
                max_attempts=1
            )

            if ai_solved:
                # Wait for Cloudflare captcha modal to disappear before verifying
                if self._wait_for_cloudflare_modal_disappear():
                    captcha_solved = self._verify_captcha(self.browser)
                    if captcha_solved:
                        self.logger.info("Captcha solved successfully on first attempt")
                        return True
                else:
                    self.logger.warning("Cloudflare modal did not disappear within timeout on first attempt")

                # Second attempt if first verification failed
                self.logger.info("First captcha verification failed, attempting second solve")
                ai_solved = solve_captcha_automatically(
                    captcha_bbox,
                    slide_button_coords,
                    objects="two main objects",
                    max_attempts=2
                )

                if ai_solved:
                    # Wait for Cloudflare captcha modal to disappear before verifying
                    if self._wait_for_cloudflare_modal_disappear():
                        captcha_solved = self._verify_captcha(self.browser)
                        if captcha_solved:
                            self.logger.info("Captcha solved successfully on second attempt")
                            return True
                    else:
                        self.logger.warning("Cloudflare modal did not disappear within timeout")

            self.logger.error("Failed to solve captcha after 2 attempts")
            return False

        except Exception as e:
            self.logger.error(f"Failed Auto Solving Captcha!! {str(e)}")
            return False

    def _finalize_account_creation(self, selected_email):
        """
        Finalize account creation by handling agreement, activation, and credential saving.

        Args:
            selected_email: The email address that was created

        Returns:
            bool: True if account finalization successful, False otherwise
        """
        try:
            # Click "Agree and continue" button
            agree_selectors = [
                '//button//span[contains(text(), "Agree and continue")]',
                '//button[@data-test="create-mailbox-create-button"]',
                '//button[@class="pos-button terms-and-conditions-advanced__cta-button pos-button--cta"]',
                '//button[@type="submit" and contains(text(), "Agree and continue")]',
                '//button[.//span[contains(text(), "Agree and continue")]]',
                '//button[@pos-button="cta" and @data-test="create-mailbox-create-button" and .//span[text()="Agree and continue"]]'
            ]

            agree_clicked = False
            for selector in agree_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()
                        agree_clicked = True
                        self.logger.info("Successfully clicked 'Agree and continue' button")
                        break
                except Exception as e:
                    continue

            if not agree_clicked:
                self.logger.warning("Could not find or click 'Agree and continue' button")

            sleep(uniform(10.0, 15.0))

            # Verify account creation
            account_created, verification_failure_type = self._verify_account_creation(selected_email)
            self.logger.info(f"Account Passed Verification: {account_created} (Type: {verification_failure_type})")

            if not account_created:
                # Store the failure type for the calling method to use
                self._last_verification_failure_type = verification_failure_type
                return False

            # Click "Activate your account now" button
            activation_selectors = [
                '//button[contains(text(), "Activate your account now")]',
                '//button[contains(@class, "btn--cta") and contains(@class, "btn-lg")]',
                '//button[@id="continueButton"]',
                '//button[@id="continueButton" and contains(text(), "Activate your account now")]'
            ]

            activation_clicked = False
            for selector in activation_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()
                        activation_clicked = True
                        self.logger.info("Successfully clicked 'Activate your account now' button")
                        break
                except Exception as e:
                    continue

            if not activation_clicked:
                self.logger.warning("Could not find or click 'Activate your account now' button")

            sleep(uniform(6.0, 10.0))

            # Check if we reached the GMX mail interface
            current_url = self.browser.this_url()
            if "navigator-bs.gmx.com/mail?sid=" in current_url:
                self.logger.info("Successfully reached GMX mail interface")
                self._save_account_credentials(selected_email)
                self._save_phone_number()
                return True
            else:
                self.logger.warning(f"Did not reach expected GMX mail interface. Current URL: {current_url}")
                self._save_phone_number()
                return False

        except Exception as e:
            self.logger.error(f"Error during account finalization: {str(e)}")
            return False

    def _cleanup_session(self, cleanup_type="default"):
        """
        Clean up browser session with appropriate wait times based on cleanup type.
        Always closes browser first, then waits.

        Args:
            cleanup_type: Type of cleanup - determines wait time and actions
        """
        try:
            # Prevent multiple cleanup calls
            if hasattr(self, '_cleanup_in_progress') and self._cleanup_in_progress:
                self.logger.debug(f"Cleanup already in progress, skipping duplicate cleanup for type: {cleanup_type}")
                return

            self._cleanup_in_progress = True

            # Always close browser first, regardless of cleanup type
            self.logger.info(f"Closing browser for cleanup type: {cleanup_type}")

            # Properly close browser through enhanced driver
            if self._enhanced_driver and hasattr(self._enhanced_driver, 'quit'):
                self.logger.info("Closing browser through enhanced driver")
                self._enhanced_driver.quit()
            elif hasattr(self, 'browser') and self.browser:
                # Fallback: try to close browser directly
                try:
                    if hasattr(self.browser, 'quit'):
                        self.logger.info("Closing browser directly")
                        self.browser.quit()
                    elif hasattr(self.browser, '_enhanced_driver') and hasattr(self.browser._enhanced_driver, 'quit'):
                        self.logger.info("Closing browser through browser's enhanced driver")
                        self.browser._enhanced_driver.quit()
                except Exception as browser_close_error:
                    self.logger.warning(f"Error closing browser directly: {browser_close_error}")

            # Clean up temporary profile and SB context
            if self._enhanced_driver and hasattr(self._enhanced_driver, 'cleanup_all'):
                self.logger.info("Cleaning up temporary profile and SB context")
                self._enhanced_driver.cleanup_all()

            # Also terminate any remaining processes as backup
            self.terminate_selenium_driver()

            # Give browser time to fully close
            sleep(2)

            # Then wait based on cleanup type
            if cleanup_type == "success":
                self.logger.info("Waiting after successful account creation")
                sleep(uniform(10.5,12.5))  # Additional wait after successful creation

            elif cleanup_type == "reject":
                self.logger.info("Waiting after reject page encounter")
                self.countdown_timer(uniform(25.0,26.0))  # Original reject page wait time with countdown

            elif cleanup_type == "captcha_failed":
                self.logger.info("Waiting after captcha failure")
                self.countdown_timer(uniform(25.0,26.0))  # Wait before next attempt with countdown

            elif cleanup_type == "registration_failed":
                self.logger.info("Waiting after registration failure")
                self.countdown_timer(uniform(25.0,26.0))  # Wait before next attempt with countdown

            elif cleanup_type == "finalization_failed":
                self.logger.info("Waiting after finalization failure")
                wait_duration = uniform(25.0,26.0)  # Original finalization failure wait time
                self.countdown_timer(wait_duration)

            else:  # error or default
                self.logger.info("Waiting after error or default case")
                wait_duration = uniform(25.0,26.0)  # Default wait time
                self.countdown_timer(wait_duration)

            sleep(uniform(8.0, 10.0))  # Additional random wait as in original code

        except Exception as e:
            self.logger.error(f"Error during session cleanup: {str(e)}")
            try:
                self.terminate_selenium_driver()
            except:
                pass
        finally:
            # Reset cleanup flag
            self._cleanup_in_progress = False



    def countdown_timer(self, duration):
        """
        Display a real-time countdown timer for the specified duration.

        Args:
            duration (float): Duration in seconds to count down from
        """
        total_seconds = int(duration)
        self.logger.info(f"Starting countdown timer for {total_seconds} seconds ({total_seconds//60}:{total_seconds%60:02d})")

        # Print initial message without newline
        print("", end="", flush=True)

        for remaining in range(total_seconds, 0, -1):
            minutes = remaining // 60
            seconds = remaining % 60

            # Create the countdown display with padding to clear previous text
            countdown_text = f"\rTime remaining: {minutes}:{seconds:02d}    "

            # Print the countdown (overwriting the previous line)
            print(countdown_text, end="", flush=True)

            # Wait for 1 second
            time.sleep(1)

        # Clear the countdown line and show completion
        print("\rCountdown complete!                    ")
        self.logger.info("Countdown timer completed")

    def run(self):
        """
        Main execution method for GMX account creation and management.

        Handles account creation, verification, and protocol activation based on selected actions.
        """
        self.ac = 0
        account_count = 0
        # Initialize account creation statistics
        self.successful_accounts = 0
        self.failed_accounts = 0
        # Check if creating new GMX accounts (temporary or persistent)
        if "create_gmx_temporary" in self.actions or "create_gmx_persistent" in self.actions:
            try:
                account_count = int(input("How many GMX accounts do you want to create? "))
                if account_count <= 0:
                    self.logger.error("Number of accounts must be greater than 0")
                    return self.run()
                self.logger.info(f"Will create {account_count} GMX accounts")
            except ValueError:
                self.logger.error("Invalid input. Please enter a valid number.")
                return self.run()
            except KeyboardInterrupt:
                self.logger.info("Operation cancelled by user")
                return self.run()

        # Check if verifying or activating protocols for existing accounts
        elif "activate_gmx_protocols" in self.actions:
            try:
                if os.path.exists(gmx_map_file):
                    with open(gmx_map_file, 'r') as f:
                        existing_accounts = f.readlines()
                    account_count = len(existing_accounts)

                    if account_count == 0:
                        self.logger.warning("No existing accounts found in the JSON file")
                        return
                    self.logger.info(f"Will process {account_count} existing GMX accounts")
                    #self.
                else:
                    self.logger.error(f"Accounts file not found: {gmx_map_file}")
                    return
            except json.JSONDecodeError:
                self.logger.error("Error reading accounts file: Invalid JSON format")
                return
            except Exception as e:
                self.logger.error(f"Error reading accounts file: {str(e)}")
                return
            

        # Process accounts based on action type
        if "create_gmx_temporary" in self.actions or "create_gmx_persistent" in self.actions:
            # Create new accounts with smart failure handling
            legitimate_attempts = 0  # Only count success + abuse failures
            total_attempts = 0       # Count all attempts for logging

            while legitimate_attempts < account_count:
                total_attempts += 1
                self.ac += 1
                print("\n")
                self.logger.info(f"====== Creating Account : {self.ac} (Legitimate: {legitimate_attempts + 1}/{account_count}, Total: {total_attempts}) ======")

                # Create single account with detailed failure type detection
                account_success, failure_type = self._create_single_account()

                # Determine if this attempt should count toward the requested total
                # SUCCESS and ABUSE failures count toward total, others trigger retry
                should_count = account_success or failure_type == "abuse"

                if should_count:
                    legitimate_attempts += 1

                # Update statistics based on account creation result
                if account_success:
                    self.successful_accounts += 1
                else:
                    self.failed_accounts += 1

                # Log detailed summary with failure type information
                self.logger.info(f"Result: {'SUCCESS' if account_success else f'FAILED ({failure_type.upper()})'}")

                if failure_type == "reject":
                    self.logger.info("Count toward total: NO (reject page - will retry)")
                elif failure_type == "no_suggestions":
                    self.logger.info("Count toward total: NO (no email suggestions - will retry)")
                elif failure_type == "abuse":
                    self.logger.info("Count toward total: YES (abuse detection counts as legitimate attempt)")
                elif account_success:
                    self.logger.info("Count toward total: YES (successful account)")
                else:
                    self.logger.info("Count toward total: NO (other failure - will retry)")

                success_rate = (self.successful_accounts / total_attempts * 100) if total_attempts > 0 else 0
                self.logger.info(f"Statistics: {self.successful_accounts} successful, {self.failed_accounts} failed, {success_rate:.1f}% success rate")
                self.logger.info(f"Progress: {legitimate_attempts}/{account_count} legitimate attempts completed")

                # Add wait between attempts
                if account_success and legitimate_attempts < account_count:
                    self.logger.info("Account created successfully. Waiting before next account creation...")
                    wait_duration = uniform(20.0, 40.0)
                    self.countdown_timer(wait_duration)
                elif not should_count:
                    self.logger.info("Retryable failure detected. Brief wait before retry...")
                    wait_duration = uniform(5.0, 10.0)
                    self.countdown_timer(wait_duration)

        else:
            # Handle existing accounts for verification or protocol activation
            for account in existing_accounts:
                self.ac += 1
                print("\n")
                self.logger.info(f"====== Processing Existing Account : {self.ac} ======")
                self.email, self.password, _,self.birthday,self.first_name,self.last_name= account.strip().split(":")

                self.logger.info(f"Processing account: {self.email}")

                try:
                    if not self._setup_browser_session(action_type="activate"):
                        self.logger.error("Failed to setup browser session")
                        return False

                    # Check for IP restriction/reject page
                    current_url = self.browser.this_url()
                    self.logger.info(f"DEBUG: Current URL after navigation: {current_url}")

                    if "activate_gmx_protocols" in self.actions:
                        self._enable_pop_imap_smtp()

                    # Handle verification or protocol activation logic here
                    if "verify_gmx_accounts" in self.actions:
                        self.logger.info("Performing SMS verification for existing account")
                        # Add verification logic here
                    elif "activate_gmx_protocols" in self.actions:
                        self.logger.info("Activating POP/IMAP/SMTP protocols for existing account")
                        # Add protocol activation logic here

                    # Cleanup
                    try:
                        current_url = None
                        try:
                            if hasattr(self, 'browser') and self.browser:
                                current_url = self.browser.this_url()
                        except Exception as url_error:
                            self.logger.debug(f"Could not get current URL during cleanup: {str(url_error)}")

                        # Only check URL patterns if current_url is not None
                        if current_url and any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                            self.logger.warning(f"Still on sign-in page: {current_url}")
                            self.logger.warning("Not closing browser - manual intervention may be needed")
                        else:
                            self.terminate_selenium_driver()
                    except Exception as cleanup_error:
                        self.logger.error(f"Cleanup error: {str(cleanup_error)}")
                        # Ensure cleanup happens even if there's an error
                        try:
                            self.terminate_selenium_driver()
                        except:
                            pass

                except Exception as e:
                    self.logger.error(f"Error processing account {self.email}: {str(e)}")
                    try:
                        if hasattr(self, 'browser') and self.browser:
                            self.terminate_selenium_driver()
                    except:
                        pass

        try:
            # Final safety check: Don't close browser if still on sign-in page
            current_url = None
            try:
                if hasattr(self, 'browser') and self.browser:
                    current_url = self.browser.this_url()
            except Exception as url_error:
                self.logger.debug(f"Could not get current URL: {str(url_error)}")
                current_url = None

            self.logger.info(f"DEBUG: Final cleanup - Browser running: {hasattr(self, 'browser') and hasattr(self.browser, 'running') and self.browser.running()}")
            self.logger.info(f"DEBUG: Final cleanup - Current URL: {current_url}")

            # Only check URL patterns if current_url is not None
            if current_url and any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                self.logger.warning(f"Final cleanup: Still on sign-in page: {current_url}")
                self.logger.warning("Final cleanup: Not closing browser - manual intervention may be needed")
                self.logger.warning("Final cleanup: Check if 'login' action is included in the script execution")
                self.logger.info("DEBUG: Final cleanup - Skipping browser.finish() due to sign-in page")
            else:
                self.logger.info("DEBUG: Final cleanup - Calling browser.finish()")
                self.terminate_selenium_driver()
        except Exception as e:
            self.logger.error(f"DEBUG: Final cleanup exception: {str(e)}")
            # Ensure cleanup happens even if there's an error
            try:
                self.terminate_selenium_driver()
            except:
                pass



class Main():
    def __init__(self) -> None:
        # Setup centralized logging
        setup_logging()
        self.logger = logging.getLogger("Main")
        self.logger.info(f"Starting GMX Creator on User : {os.getlogin()} !!")

        start_time = time.time()

        # Pre-flight validation: Check OpenRouter API credits first
        self.logger.info("Starting pre-flight validation checks...")

        # Load minimum credit threshold from config or use default
        min_credit_threshold = self.get_min_credit_threshold()

        credits_ok, available_credits, credits_message = self.check_openrouter_credits(min_credit_threshold)
        if not credits_ok:
            self.logger.error("Pre-flight validation failed: OpenRouter credits check failed")
            self.logger.error(f"Error: {credits_message}")
            self.logger.error("Stopping execution - AI functionality requires sufficient OpenRouter credits")
            self.logger.error("Please add credits at: https://openrouter.ai/credits")
            sys.exit(1)

        self.logger.info("OpenRouter credits check passed - proceeding with startup")

        # Check captcha service balance
        balance = self.get_balance()
        if balance == "-0.0":
            print("Captcha out of Balance")
        else:
            while True:
                if len(sys.argv) > 1:
                    answ = sys.argv[1]
                else:
                    answ = self.questions()
                if answ == "1":
                    # Create GMX Accounts with Temporary Profiles
                    actions = ["create_gmx_temporary"]
                    break
                elif answ == "2":
                    # Create GMX Accounts with Persistent Profiles
                    actions = ["create_gmx_persistent"]
                    break
                elif answ == "3":
                    # Verify GMX Accounts
                    actions = ["verify_gmx_accounts"]
                    break
                elif answ == "4":
                    # Activate POP/IMAP/SMTP
                    actions = ["activate_gmx_protocols"]
                    break
                else:
                    self.logger.info("Invalid choice, please try again.")
                    if len(sys.argv) > 1:
                        sys.exit(1)


        worker = Worker(actions)
        worker.run()

        end_time = time.time()
        execution_time = end_time - start_time
        execution_time_hours = execution_time / 3600
        self.logger.info(f"Script execution time: {execution_time_hours:.2f} hours")



    def check_openrouter_credits(self, min_threshold=1.0):
        """
        Check OpenRouter API credits availability before starting operations.

        Args:
            min_threshold (float): Minimum credit threshold required to proceed

        Returns:
            tuple: (success: bool, credits: float, message: str)
        """
        try:

            self.logger.info(" Checking OpenRouter API credits availability...")
            api_manager = get_api_manager()
            success, credits, message = api_manager.check_credits(min_threshold)

            if success:
                self.logger.info(f"Sufficient credits available (${credits:.4f} >= ${min_threshold:.2f})")
                self.logger.info(message)
            else:
                self.logger.error(message)
                if credits > 0:
                    self.logger.error("Please add credits to your OpenRouter account at: https://openrouter.ai/credits")

            return success, credits, message

        except ImportError:
            error_msg = "API key manager not available - falling back to legacy method"
            self.logger.warning(error_msg)
            return self._check_openrouter_credits_legacy(min_threshold)

        except Exception as e:
            error_msg = f"Unexpected error checking OpenRouter credits: {str(e)}"
            self.logger.error(error_msg)
            return False, 0.0, error_msg



    def _check_openrouter_credits_legacy(self, min_threshold=1.0):
        """
        Legacy method for checking OpenRouter credits (fallback).
        """
        try:
            # Fallback to hardcoded key if API manager fails
            openrouter_api_key = "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"
            openrouter_api_url = "https://openrouter.ai/api/v1/credits"

            self.logger.info("Using legacy credit check method...")

            # Make API request to check credits
            headers = {
                'Authorization': f'Bearer {openrouter_api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'GMX-Creator/1.0'
            }

            response = requests.get(openrouter_api_url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                self.logger.debug(f"OpenRouter API Response: {json.dumps(data, indent=2)}")

                # Extract credit information from response
                if 'data' in data:
                    credit_info = data['data']
                    available_credits = 0.0

                    if isinstance(credit_info, dict):
                        total_credits = credit_info.get('total_credits', 0.0)
                        total_usage = credit_info.get('total_usage', 0.0)
                        available_credits = float(total_credits) - float(total_usage)
                        self.logger.debug(f"Total credits: ${total_credits}, Total usage: ${total_usage}")
                    else:
                        self.logger.warning("Unexpected credit_info format - not a dictionary")
                        available_credits = 0.0

                    self.logger.info(f"OpenRouter Credits Available: ${available_credits:.4f}")

                    if available_credits >= min_threshold:
                        self.logger.info(f"Sufficient credits available (${available_credits:.4f} >= ${min_threshold:.2f})")
                        return True, available_credits, "Sufficient credits available"
                    else:
                        error_msg = f"Insufficient OpenRouter credits: ${available_credits:.4f} < ${min_threshold:.2f} required"
                        self.logger.error(error_msg)
                        self.logger.error(" Please add credits to your OpenRouter account at: https://openrouter.ai/credits")
                        return False, available_credits, error_msg
                else:
                    self.logger.warning("️ Unexpected API response format - could not determine credit balance")
                    return False, 0.0, "Could not determine credit balance from API response"

            elif response.status_code == 401:
                error_msg = "OpenRouter API authentication failed - invalid API key"
                self.logger.error(error_msg)
                return False, 0.0, error_msg
            else:
                error_msg = f"OpenRouter API request failed with status {response.status_code}: {response.text}"
                self.logger.error(error_msg)
                return False, 0.0, error_msg

        except requests.exceptions.Timeout:
            error_msg = "OpenRouter API request timed out - check internet connection"
            self.logger.error(error_msg)
            return False, 0.0, error_msg
        except requests.exceptions.ConnectionError:
            error_msg = "Could not connect to OpenRouter API - check internet connection"
            self.logger.error(error_msg)
            return False, 0.0, error_msg
        except Exception as e:
            error_msg = f"Unexpected error in legacy credit check: {str(e)}"
            self.logger.error(error_msg)
            return False, 0.0, error_msg



    def get_min_credit_threshold(self):
        """
        Get the minimum credit threshold from configuration or return default.

        Returns:
            float: Minimum credit threshold required to proceed
        """
        try:
            # Try to load from enhanced settings first
            if os.path.exists(f"{home}/json/enhanced_settings.json"):
                with open(f"{home}/json/enhanced_settings.json", 'r') as f:
                    settings = json.load(f)
                    openrouter_config = settings.get('openrouter', {})
                    threshold = openrouter_config.get('min_credit_threshold', 1.0)
                    self.logger.debug(f"Loaded min credit threshold from enhanced_settings.json: ${threshold}")
                    return float(threshold)

            # Try to load from regular settings
            elif os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                    threshold = settings.get('openrouter_min_credits', 1.0)
                    self.logger.debug(f"Loaded min credit threshold from settings.json: ${threshold}")
                    return float(threshold)

        except Exception as e:
            self.logger.debug(f"Could not load min credit threshold from settings: {str(e)}")

        # Return default threshold
        default_threshold = 1.0
        self.logger.debug(f"Using default min credit threshold: ${default_threshold}")
        return default_threshold

    def save_openrouter_config(self, min_credit_threshold=1.0):
        """
        Save OpenRouter configuration to settings file.

        Args:
            min_credit_threshold (float): Minimum credit threshold to save
        """
        try:
            config_path = f"{home}/json/enhanced_settings.json"

            # Load existing settings or create new
            settings = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r') as f:
                        settings = json.load(f)
                except Exception as e:
                    self.logger.warning(f"Could not load existing settings: {str(e)}")
                    settings = {}

            # Update OpenRouter configuration
            if 'openrouter' not in settings:
                settings['openrouter'] = {}

            settings['openrouter']['min_credit_threshold'] = float(min_credit_threshold)
            settings['openrouter']['api_url'] = "https://openrouter.ai/api/v1"
            settings['openrouter']['credits_check_url'] = "https://openrouter.ai/api/v1/auth/key"

            # Ensure directory exists
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # Save updated settings
            with open(config_path, 'w') as f:
                json.dump(settings, f, indent=4)

            self.logger.info(f"✅ OpenRouter configuration saved: min_credit_threshold=${min_credit_threshold}")

        except Exception as e:
            self.logger.error(f"Failed to save OpenRouter configuration: {str(e)}")

    def get_balance(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            balance = solver.balance()
            balance = float(str(balance)[:4])
        except Exception as e:
            self.logger.error(f"{str(e)}")
            balance = 0

        return balance




    def questions(self):
        """
        if not os.path.exists(settings_path):
            use_proxy = input("settings.json not found. Do you want to use a proxy? (yes/no): ").strip().lower()
            if use_proxy.lower() in ['y', 'yes']:
                if os.path.exists(proxy_file):
                    with open(proxy_file, 'r') as prx_file:
                        proxies = prx_file.readlines()
                    if proxies:
                        proxy = random.choice(proxies).strip()
                        settings = {
                            'use_proxy': True,
                            'proxy': proxy
                        }
                        with open(settings_path, 'w') as settings_file:
                            json.dump(settings, settings_file, indent=4)
                        self.logger.info("Proxy settings saved to settings.json")
                    else:
                        self.logger.error("proxy.txt is empty.")
                else:
                    self.logger.error("proxy.txt not found.")
            else:
                settings = {
                    'use_proxy': False
                }
                with open(settings_path, 'w') as settings_file:
                    json.dump(settings, settings_file, indent=4)
                self.logger.info("Settings saved to settings.json without proxy")
        """
        self.logger.info("### GMX Email Creator - What do you want to do? ###")
        self.logger.info(""" 1. Create GMX Accounts with Temporary Profiles""")
        self.logger.info(""" 2. Create GMX Accounts with Persistent Profiles""")
        self.logger.info(""" 3. Verify GMX Accounts""")
        self.logger.info(""" 4. Activate POP/IMAP/SMTP""")
        return input("Please enter your choice: ")

Main()

