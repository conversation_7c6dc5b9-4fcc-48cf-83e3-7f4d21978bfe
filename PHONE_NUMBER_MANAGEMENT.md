# Phone Number Management System

This document describes the enhanced phone number management features implemented for the GMX Creator system.

## Overview

The new phone number management system provides three key features:

1. **Phone Number Bulk Cancellation**: Automatically order and cancel multiple phone numbers to avoid charges
2. **Sequential Phone Number Usage Strategy**: Hybrid approach alternating between file-based and 5sim API sources
3. **Thread-Safe Operations**: Proper locking mechanisms for concurrent access

## Features

### 1. Phone Number Bulk Cancellation

The system can order multiple phone numbers from 5sim and immediately cancel them to avoid unnecessary charges.

**Implementation:**
- Located in `FiveSimManager.bulk_order_and_cancel_phone_numbers()`
- Orders specified number of phone numbers (default: 10)
- Automatically cancels all ordered numbers
- Includes rate limiting and error handling

**Usage:**
```python
# Through PhoneNumberCoordinator
coordinator = PhoneNumberCoordinator()
success = coordinator.bulk_cancel_phone_numbers(count=10, country='england')

# Or directly through FiveSimManager
fivesim_manager = FiveSimManager(logger)
success = fivesim_manager.bulk_order_and_cancel_phone_numbers(10, 'england')
```

### 2. Sequential Phone Number Usage Strategy

The system alternates between two phone number sources:
- **Odd requests (1st, 3rd, 5th, ...)**: Used phone numbers file
- **Even requests (2nd, 4th, 6th, ...)**: 5sim API

**Benefits:**
- Reduces 5sim API costs by reusing existing numbers
- Maintains variety through alternating sources
- Automatic fallback if one source fails

**Implementation:**
- Centralized in `PhoneNumberCoordinator` class
- Thread-safe counter management
- Automatic fallback between sources

### 3. Phone Number Modification

All phone numbers (from both sources) undergo randomized modification:
- **50% chance**: Change 4 center digits
- **25% chance**: Change last 2 digits  
- **25% chance**: Use original number

This ensures realistic variation while maintaining valid phone number formats.

### 4. Thread Safety

The system includes comprehensive thread safety mechanisms:

**File Access:**
- File-specific locks for each country code
- Singleton pattern for coordinator instance
- Context manager usage for proper lock handling

**Counter Management:**
- Thread-safe usage counter with dedicated lock
- Atomic increment operations
- Consistent sequential behavior across threads

## Architecture

### PhoneNumberCoordinator Class

The central coordinator class manages all phone number operations:

```python
class PhoneNumberCoordinator:
    # Singleton pattern ensures consistent state
    _instance = None
    _lock = threading.Lock()
    _file_locks = {}  # Country-specific file locks
    
    def get_phone_number_sequential(self, country, fivesim_manager):
        # Main method for sequential phone number retrieval
        
    def _get_phone_from_file_thread_safe(self, country):
        # Thread-safe file-based phone number retrieval
        
    def _get_phone_from_5sim(self, country, fivesim_manager):
        # 5sim API phone number retrieval
        
    def _apply_phone_modification(self, clean_phone):
        # Consistent phone number modification logic
```

### Integration with Existing System

The new system integrates seamlessly with the existing GMX Creator:

1. **Worker Class**: Updated `_get_phone_number()` method uses the coordinator
2. **FiveSimManager**: Enhanced with bulk cancellation capabilities
3. **File Structure**: Maintains existing phone number file format
4. **Logging**: Comprehensive logging for debugging and monitoring

## File Structure

The system uses country-specific phone number files:

```
credentials/
├── ca_used_phone_numbers.txt    # Canada
├── de_used_phone_numbers.txt    # Germany  
├── gb_used_phone_numbers.txt    # England
└── usa_used_phone_numbers.txt   # USA
```

## Usage Examples

### Basic Phone Number Retrieval

```python
# Initialize coordinator
coordinator = PhoneNumberCoordinator()
fivesim_manager = FiveSimManager(logger)

# Get phone number using sequential strategy
phone_number = coordinator.get_phone_number_sequential('england', fivesim_manager)
```

### Bulk Cancellation

```python
# Cancel 10 phone numbers to avoid charges
coordinator = PhoneNumberCoordinator()
success = coordinator.bulk_cancel_phone_numbers(count=10, country='england')
```

### Integration in Account Creation

The system automatically integrates with existing account creation:

```python
# In Worker._get_phone_number()
coordinator = PhoneNumberCoordinator()
fivesim_manager = FiveSimManager(self.logger)
phone_number = coordinator.get_phone_number_sequential(country, fivesim_manager)
```

## Configuration

### Default Settings

- **Bulk cancellation count**: 10 phone numbers
- **Default country**: 'england'
- **Modification probabilities**: 50% center, 25% last digits, 25% original
- **File encoding**: UTF-8

### Customization

All parameters can be customized when calling the methods:

```python
# Custom bulk cancellation
coordinator.bulk_cancel_phone_numbers(count=5, country='germany')

# The modification logic is built-in but can be extended
```

## Error Handling

The system includes comprehensive error handling:

- **File access errors**: Graceful fallback to alternative source
- **API failures**: Automatic retry with fallback
- **Threading issues**: Proper lock management prevents deadlocks
- **Invalid phone numbers**: Validation and cleaning logic

## Logging

Detailed logging is provided for monitoring and debugging:

```
INFO: Phone number request #1: file source for england
INFO: Selected phone number from file: 1234567890
INFO: Applied center digits modification: 1234567890 -> 1298767890
INFO: Phone number request #2: 5sim source for england
INFO: Successfully got phone number from 5sim: 0987654321
```

## Performance Considerations

- **File locking**: Minimal lock duration to prevent bottlenecks
- **Singleton pattern**: Reduces memory overhead
- **Rate limiting**: Built-in delays prevent API rate limiting
- **Fallback logic**: Ensures high availability

## Future Enhancements

Potential improvements for future versions:

1. **Phone number pool management**: Pre-load and manage phone number pools
2. **Usage statistics**: Track success rates by source
3. **Dynamic strategy**: Adjust strategy based on success rates
4. **Configuration file**: External configuration for parameters
5. **Database integration**: Store phone numbers in database instead of files

## Troubleshooting

### Common Issues

1. **File not found**: System automatically creates missing directories
2. **Empty phone number files**: Automatic fallback to 5sim API
3. **5sim API failures**: Fallback to file-based numbers
4. **Thread conflicts**: Built-in locking prevents race conditions

### Debug Information

Enable debug logging to see detailed operation flow:

```python
logging.getLogger("PhoneNumberCoordinator").setLevel(logging.DEBUG)
```

This will show detailed information about source selection, file operations, and phone number modifications.
