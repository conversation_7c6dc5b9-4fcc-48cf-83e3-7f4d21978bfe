"""
5sim API Integration Module for Gmail Phone Verification
Provides automatic phone number acquisition and SMS verification for Gmail login automation.
"""

import requests
import json
import time
import random
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum


class FiveSimError(Exception):
    """Custom exception for 5sim API errors"""
    pass


class OrderStatus(Enum):
    """5sim order status enumeration"""
    PENDING = "PENDING"
    RECEIVED = "RECEIVED"
    CANCELED = "CANCELED"
    TIMEOUT = "TIMEOUT"
    FINISHED = "FINISHED"
    BANNED = "BANNED"


@dataclass
class SMSMessage:
    """SMS message data structure"""
    id: int
    created_at: str
    date: str
    sender: str
    text: str
    code: str


@dataclass
class PhoneOrder:
    """Phone number order data structure"""
    id: int
    phone: str
    operator: str
    product: str
    price: float
    status: str
    expires: str
    sms: List[SMSMessage]
    created_at: str
    country: str


class FiveSimClient:
    """
    5sim API client for phone number verification
    Integrates with existing Gmail automation workflow
    """
    
    def __init__(self, api_key: str, logger: Optional[logging.Logger] = None):
        """
        Initialize 5sim client

        Args:
            api_key: 5sim API key
            logger: Logger instance for debugging
        """
        if not api_key or not api_key.strip():
            raise ValueError("API key is required and cannot be empty")

        self.api_key = api_key.strip()
        self.base_url = "https://5sim.net/v1"
        self.logger = logger or logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Configuration
        self.default_country = "england"  # Use 5sim format for England (GB)
        self.default_operator = "any"
        self.product = "gmx"  # For Gmail verification
        self.max_wait_time = 300  # 5 minutes max wait for SMS
        self.check_interval = 10  # Check for SMS every 10 seconds

        # Validation
        self._validate_configuration()




    def _validate_configuration(self):
        """Validate the client configuration"""
        try:
            # Test API key by getting profile
            response = self.session.get(f"{self.base_url}/user/profile", timeout=10)
            if response.status_code == 401:
                raise FiveSimError("Invalid API key - authentication failed")
            elif response.status_code != 200:
                self.logger.warning(f"API validation returned status {response.status_code}")
            else:
                self.logger.info("5sim API key validated successfully")
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"Could not validate API key: {str(e)}")
        except Exception as e:
            self.logger.warning(f"API validation error: {str(e)}")

    def _validate_country_code(self, country: str) -> str:
        """Validate and normalize country code to 5sim format"""
        if not country:
            return "gb"

        country = country.lower().strip()

        # Map common country codes to 5sim format
        country_mapping = {
            "gb": "england",
            "uk": "england",
            "england": "england",
            "britain": "england",
            "united_kingdom": "england",
        }

        if country in country_mapping:
            mapped_country = country_mapping[country]
            self.logger.debug(f"Mapped country '{country}' to '{mapped_country}'")
            return mapped_country
        else:
            self.logger.warning(f"Invalid country code '{country}', using 'UK' instead")
            return "gb"

    def _log_api_request(self, method: str, url: str, response: requests.Response):
        """Log API request details for debugging"""
        self.logger.debug(f"API {method} {url}")
        self.logger.debug(f"Response: {response.status_code} - {response.reason}")
        if response.status_code >= 400:
            self.logger.error(f"API Error Response: {response.text}")

    def get_balance(self) -> float:
        """Get account balance"""
        try:
            url = f"{self.base_url}/user/profile"
            response = self.session.get(url, timeout=10)
            self._log_api_request("GET", url, response)
            response.raise_for_status()

            data = response.json()
            balance = data.get('balance', 0)
            self.logger.info(f"5sim account balance: ${balance}")
            return balance

        except requests.exceptions.Timeout:
            self.logger.error("Timeout getting 5sim balance")
            raise FiveSimError("Timeout getting balance - check network connection")
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error getting 5sim balance: {e.response.status_code}")
            raise FiveSimError(f"HTTP error getting balance: {e.response.status_code}")
        except Exception as e:
            self.logger.error(f"Failed to get 5sim balance: {str(e)}")
            raise FiveSimError(f"Failed to get balance: {str(e)}")
    
    def get_available_numbers(self, country: str = None, operator: str = None) -> Dict[str, Any]:
        """Get available phone numbers for the product"""
        try:
            # Validate and normalize inputs
            operator = operator or self.default_operator

            url = f"{self.base_url}/guest/products/{country}/{operator}"
            self.logger.debug(f"Checking availability at: {url}")

            response = self.session.get(url, timeout=10)
            self._log_api_request("GET", url, response)
            response.raise_for_status()

            data = response.json()
            google_info = data.get('google', {})

            if not google_info:
                self.logger.warning(f"No Google verification numbers available for {country}/{operator}")
                return {}

            qty = google_info.get('Qty', 0)
            price = google_info.get('Price', 0)
            self.logger.info(f"Available Google numbers for {country}: {qty} at ${price}")
            return google_info

        except requests.exceptions.Timeout:
            self.logger.error("Timeout checking number availability")
            raise FiveSimError("Timeout checking availability - check network connection")
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error getting available numbers: {e.response.status_code} - {e.response.text}")
            raise FiveSimError(f"HTTP error getting available numbers: {e.response.status_code}")
        except Exception as e:
            self.logger.error(f"Failed to get available numbers: {str(e)}")
            raise FiveSimError(f"Failed to get available numbers: {str(e)}")
    

    def buy_phone_number(self, country: str = None, operator: str = None) -> PhoneOrder:
        """
        Purchase a phone number for Gmail verification

        Args:
            country: Country code (default: any)
            operator: Operator name (default: any)

        Returns:
            PhoneOrder object with phone number details
        """
        try:
            # Validate and sanitize inputs
            operator = operator or self.default_operator

            self.logger.info(f"Attempting to buy phone number from {country} with operator {operator}")

            # Check balance first
            balance = self.get_balance()
            if balance < 0.5:  # Minimum balance check
                raise FiveSimError(f"Insufficient balance: ${balance}. Please top up your 5sim account.")

            # Check availability
            availability = self.get_available_numbers(country, operator)
            if not availability or availability.get('Qty', 0) == 0:
                raise FiveSimError(f"No phone numbers available for Google verification in {country}")

            # Purchase number
            url = f"{self.base_url}/user/buy/activation/{country}/{operator}/{self.product}"
            self.logger.info(f"Purchasing phone number from: {url}")

            response = self.session.get(url, timeout=15)
            self._log_api_request("GET", url, response)
            response.raise_for_status()

            data = response.json()

            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:  # Handle null SMS entries
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))

            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )

            self.logger.info(f"Successfully purchased phone number: {order.phone} from {order.country} (Order ID: {order.id})")
            return order

        except requests.exceptions.Timeout:
            self.logger.error("Timeout purchasing phone number")
            raise FiveSimError("Timeout purchasing phone number - check network connection")
        except requests.exceptions.HTTPError as e:
            error_details = ""
            try:
                error_data = e.response.json()
                error_details = f" - {error_data}"
            except:
                error_details = f" - {e.response.text}"

            if e.response.status_code == 400:
                error_msg = f"Bad request for {country}/{operator}/google{error_details}"
                self.logger.error(f"API returned 400 - possible issues: invalid country/operator, no numbers available, insufficient balance")
            elif e.response.status_code == 404:
                error_msg = f"Service not found for {country}/{operator}/google"
                self.logger.error(f"API returned 404 - service may not be available for this country/operator combination")
            elif e.response.status_code == 401:
                error_msg = "Authentication failed - check API key"
                self.logger.error("API returned 401 - API key may be invalid or expired")
            else:
                error_msg = f"HTTP error {e.response.status_code}{error_details}"

            self.logger.error(f"Failed to buy phone number: {error_msg}")
            raise FiveSimError(f"Failed to buy phone number: {error_msg}")
        except Exception as e:
            self.logger.error(f"Unexpected error buying phone number: {str(e)}")
            raise FiveSimError(f"Failed to buy phone number: {str(e)}")
    
    def check_sms(self, order_id: int) -> PhoneOrder:
        """
        Check for SMS messages on a phone number order
        
        Args:
            order_id: Order ID from phone number purchase
            
        Returns:
            Updated PhoneOrder with any new SMS messages
        """
        try:
            url = f"{self.base_url}/user/check/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:  # Handle null SMS entries
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))
            
            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )
            
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to check SMS for order {order_id}: {str(e)}")
            raise FiveSimError(f"Failed to check SMS: {str(e)}")
    
    def wait_for_sms(self, order_id: int, timeout: int = None) -> Optional[str]:
        """
        Wait for SMS verification code with enhanced polling strategy

        Args:
            order_id: Order ID from phone number purchase
            timeout: Maximum wait time in seconds (default: 300)

        Returns:
            Verification code if received, None if timeout
        """
        timeout = timeout or self.max_wait_time
        start_time = time.time()

        # Enhanced polling strategy with exponential backoff
        initial_interval = 3  # Start with 3 seconds (faster than 10)
        max_interval = 15     # Cap at 15 seconds
        current_interval = initial_interval
        backoff_multiplier = 1.2
        consecutive_errors = 0
        max_consecutive_errors = 3

        self.logger.info(f"Starting SMS polling for order {order_id} with {timeout}s timeout")

        while time.time() - start_time < timeout:
            try:
                order = self.check_sms(order_id)
                consecutive_errors = 0  # Reset error counter on successful API call

                # Check if we have SMS messages
                if order.sms:
                    for sms in order.sms:
                        if sms.code:  # 5sim automatically extracts codes
                            self.logger.info(f"Received SMS verification code: {sms.code}")
                            return sms.code
                        elif sms.text:
                            # Enhanced fallback: extract code from text with multiple patterns
                            code = self._extract_verification_code(sms.text)
                            if code:
                                self.logger.info(f"Extracted verification code from SMS: {code}")
                                return code

                # Check order status
                if order.status in [OrderStatus.TIMEOUT.value, OrderStatus.CANCELED.value, OrderStatus.BANNED.value]:
                    self.logger.warning(f"Order {order_id} status changed to {order.status}")
                    break

                # Dynamic interval adjustment - faster polling initially, slower over time
                elapsed = time.time() - start_time
                if elapsed < 60:  # First minute: fast polling
                    current_interval = 3
                elif elapsed < 180:  # Next 2 minutes: medium polling
                    current_interval = 5
                else:  # After 3 minutes: slower polling
                    current_interval = 8

                self.logger.debug(f"SMS polling attempt, waiting {current_interval}s (elapsed: {elapsed:.1f}s)")
                time.sleep(current_interval)

            except Exception as e:
                consecutive_errors += 1
                self.logger.error(f"Error while waiting for SMS (attempt {consecutive_errors}): {str(e)}")

                # Exponential backoff on errors
                if consecutive_errors >= max_consecutive_errors:
                    self.logger.error(f"Too many consecutive errors ({consecutive_errors}), aborting SMS wait")
                    break

                error_delay = min(current_interval * (backoff_multiplier ** consecutive_errors), max_interval)
                self.logger.debug(f"Backing off for {error_delay:.1f}s due to error")
                time.sleep(error_delay)

        self.logger.warning(f"SMS verification code not received within {timeout} seconds")
        return None

    def _extract_verification_code(self, sms_text: str) -> Optional[str]:
        """
        Enhanced verification code extraction with multiple patterns and validation

        Args:
            sms_text: SMS message text

        Returns:
            Extracted verification code or None
        """
        import re

        self.logger.debug(f"Extracting verification code from SMS: {sms_text}")

        # Multiple patterns for different SMS formats (ordered by priority)
        patterns = [
            # Google-specific patterns (highest priority)
            r'G-(\d{6})',  # Google format "G-123456"
            r'Google[:\s]+(\d{6})',  # "Google: 123456"
            r'(?:verification code|code)[:\s]*(\d{6})',  # "verification code: 123456"

            # Generic patterns for different code lengths
            r'(?:code|pin)[:\s]*(\d{6})',  # "code: 123456"
            r'Your code is[:\s]*(\d{6})',  # "Your code is: 123456"
            r'(\d{6})',  # Standard 6-digit code

            # 4-digit codes (less common for Google)
            r'(?:code|pin)[:\s]*(\d{4})',  # "code: 1234"
            r'(\d{4})',  # 4-digit code

            # 8-digit codes (rare)
            r'(?:code|pin)[:\s]*(\d{8})',  # "code: 12345678"
            r'(\d{8})',  # 8-digit code

            # Fallback pattern
            r'\b(\d{4,8})\b'  # Any 4-8 digit number (lowest priority)
        ]

        for i, pattern in enumerate(patterns):
            match = re.search(pattern, sms_text, re.IGNORECASE)
            if match:
                code = match.group(1) if match.groups() else match.group(0)

                # Enhanced validation
                if self._validate_verification_code(code, sms_text):
                    self.logger.info(f"Successfully extracted verification code using pattern {i+1}: {code}")
                    return code
                else:
                    self.logger.debug(f"Code {code} failed validation, trying next pattern")
                    continue

        self.logger.warning(f"No valid verification code found in SMS: {sms_text}")
        return None

    def _validate_verification_code(self, code: str, sms_text: str) -> bool:
        """
        Validate extracted verification code

        Args:
            code: Extracted code
            sms_text: Original SMS text

        Returns:
            True if code is valid
        """
        # Basic validation
        if not code or not code.isdigit():
            return False

        # Length validation
        if not (4 <= len(code) <= 8):
            return False

        # Google typically uses 6-digit codes
        if len(code) == 6:
            return True

        # 4-digit codes are acceptable but less common
        if len(code) == 4:
            # Additional validation for 4-digit codes
            # Avoid common false positives like years, times, etc.
            if code.startswith('20'):  # Likely a year
                return False
            if code in ['1234', '0000', '9999']:  # Common test codes
                return False
            return True

        # 8-digit codes are rare but possible
        if len(code) == 8:
            # Additional validation for 8-digit codes
            # Check if it's not a phone number or timestamp
            if 'phone' in sms_text.lower() or 'number' in sms_text.lower():
                return False
            return True

        # 5 or 7 digit codes are unusual but possible
        return True

    def finish_order(self, order_id: int) -> bool:
        """
        Mark order as finished (successful verification)
        
        Args:
            order_id: Order ID to finish
            
        Returns:
            True if successful
        """
        try:
            url = f"{self.base_url}/user/finish/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            self.logger.info(f"Successfully finished order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to finish order {order_id}: {str(e)}")
            return False
    
    def cancel_order(self, order_id: int) -> bool:
        """
        Cancel an order (if no SMS received)
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if successful
        """
        try:
            url = f"{self.base_url}/user/cancel/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            self.logger.info(f"Successfully canceled order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {str(e)}")
            return False


def load_fivesim_config(config_path: str = "fivesim_config.json") -> Dict[str, Any]:
    """Load 5sim configuration from JSON file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Validate required fields
        if config.get('enabled', False) and not config.get('api_key', ''):
            logging.warning("5sim is enabled but no API key provided")
            config['enabled'] = False

        return config

    except FileNotFoundError:
        logging.info("5sim config file not found, using defaults")
        # Return default configuration
        return {
            "api_key": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "enabled": True,
            "default_country": "england",  # Changed from "any" to "england" (GB)
            "default_operator": "any",
            "max_wait_time": 300,
            "check_interval": 10,
            "preferred_countries": ["england"],  # Popular countries for Gmail
            "fallback_to_manual": True,
            "auto_finish_orders": True,
            "min_balance_threshold": 1.0,
            "retry_attempts": 3,
            "error_handling": {
                "auto_cancel_on_timeout": True,
                "auto_retry_on_failure": True,
                "max_retry_attempts": 2
            }
        }
    except Exception as e:
        logging.error(f"Error loading 5sim config: {str(e)}")
        return {}


def save_fivesim_config(config: Dict[str, Any], config_path: str = "fivesim_config.json") -> bool:
    """Save 5sim configuration to JSON file"""
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        return True
    except Exception as e:
        logging.error(f"Error saving 5sim config: {str(e)}")
        return False


class FiveSimManager:
    """
    High-level manager for 5sim integration with Gmail automation
    Provides retry logic, error handling, and configuration management
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        #self.config = load_fivesim_config()
        self.client = None
        self.current_order = None

        api_key = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        self.client = FiveSimClient(api_key, self.logger)


    def get_phone_number_for_gmx(self,valid_country) -> Optional[str]:
        """
        Get a phone number for Gmail verification with retry logic
        Only uses preferred countries - no fallback to 'any' or Russia

        Returns:
            Phone number string if successful, None if failed
        """


        max_attempts = 3
        retry_delay = 30

        # Only use preferred countries - convert to 5sim format
        # Popular countries that work well with Gmail verification (in order of preference)
        #valid_country = 'england'


        for attempt in range(max_attempts):
            try:
                self.logger.info(f"Attempting to purchase phone number (attempt {attempt + 1}/{max_attempts})")
                self.logger.info(f"Trying countries in order: {valid_country}")


                try:
                    self.logger.info(f"Trying to get phone number from {valid_country}...")
                    order = self.client.buy_phone_number(
                        country=valid_country,
                        operator='any'
                    )

                    self.current_order = order
                    return order.phone

                except FiveSimError as e:
                    self.logger.warning(f"Failed to get number from {valid_country}: {str(e)}")
                    

                # No fallback to 'any' - only use preferred countries
                self.logger.warning(f"All preferred countries failed on attempt {attempt + 1}")

            except Exception as e:
                self.logger.error(f"Unexpected error getting phone number: {str(e)}")

            # Wait before retry (except on last attempt)
            if attempt < max_attempts - 1:
                self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                time.sleep(retry_delay)

        self.logger.error("Failed to get phone number after all attempts from preferred countries")
        return None



    def wait_for_verification_code(self, timeout: int = None) -> Optional[str]:
        """
        Wait for SMS verification code for the current order

        Args:
            timeout: Maximum wait time in seconds

        Returns:
            Verification code if received, None if timeout or error
        """
        if not self.current_order or not self.client:
            self.logger.error("No active order or client not initialized")
            return None

        timeout = timeout or 300

        try:
            code = self.client.wait_for_sms(self.current_order.id, timeout)
            return code
        except Exception as e:
            self.logger.error(f"Error waiting for verification code: {str(e)}")
            return None

    def finish_current_order(self) -> bool:
        """Mark the current order as finished"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.finish_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error finishing order: {str(e)}")
            return False

    def cancel_current_order(self) -> bool:
        """Cancel the current order"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.cancel_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error canceling order: {str(e)}")
            return False

    def bulk_order_and_cancel_phone_numbers(self, count: int = 10, country: str = 'england') -> bool:
        """
        Order multiple phone numbers and immediately cancel them to avoid charges.
        This is useful for warming up the account or testing availability.

        Args:
            count: Number of phone numbers to order and cancel (default: 10)
            country: Country to order from (default: 'england')

        Returns:
            bool: True if all operations completed successfully
        """
        if not self.client:
            self.logger.error("5sim client not initialized")
            return False

        self.logger.info(f"Starting bulk order and cancel operation for {count} phone numbers from {country}")

        ordered_numbers = []
        success_count = 0

        try:
            # Order phone numbers
            for i in range(count):
                try:
                    self.logger.info(f"Ordering phone number {i + 1}/{count}...")
                    order = self.client.buy_phone_number(country=country, operator='any')
                    ordered_numbers.append(order)
                    self.logger.info(f"Successfully ordered phone number: {order.phone} (Order ID: {order.id})")

                    # Small delay between orders to avoid rate limiting
                    time.sleep(random.uniform(0.5, 1.5))

                except Exception as e:
                    self.logger.error(f"Failed to order phone number {i + 1}: {str(e)}")
                    continue

            self.logger.info(f"Ordered {len(ordered_numbers)} phone numbers. Starting cancellation process...")

            # Cancel all ordered numbers
            for i, order in enumerate(ordered_numbers):
                try:
                    self.logger.info(f"Canceling order {i + 1}/{len(ordered_numbers)}: {order.phone} (ID: {order.id})")
                    success = self.client.cancel_order(order.id)

                    if success:
                        success_count += 1
                        self.logger.info(f"Successfully canceled order {order.id}")
                    else:
                        self.logger.warning(f"Failed to cancel order {order.id}")

                    # Small delay between cancellations
                    time.sleep(random.uniform(0.3, 0.8))

                except Exception as e:
                    self.logger.error(f"Error canceling order {order.id}: {str(e)}")
                    continue

            self.logger.info(f"Bulk operation completed. Ordered: {len(ordered_numbers)}, Canceled: {success_count}")
            return success_count == len(ordered_numbers)

        except Exception as e:
            self.logger.error(f"Error in bulk order and cancel operation: {str(e)}")
            return False
